<script setup>
const display = ref("desktop");

onMounted(() =>
  nextTick(() => {
    if (window.innerWidth < 800) {
      display.value = "mobile";
    }
  })
);

// Carousel state
const currentSlide = ref(1); // Start at 1 (first real slide)
const imagesPerSlide = 5;
const isTransitioning = ref(false);

const groundBreaking = ref([
  "https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/08.28.25%20Pictures%20for%20Posting/1-Groundbreaking.jpg",
  "https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/08.28.25%20Pictures%20for%20Posting/2-Groundbreaking.jpg",
  "https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/08.28.25%20Pictures%20for%20Posting/3-Groundbreaking.jpg",
  "https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/08.28.25%20Pictures%20for%20Posting/4-Groundbreaking.jpg",
  "https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/08.28.25%20Pictures%20for%20Posting/5-Groundbreaking.jpg",
  "https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/08.28.25%20Pictures%20for%20Posting/6-Groundbreaking.jpg",
  "https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/08.28.25%20Pictures%20for%20Posting/7-Groundbreaking.jpg",
  "https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/08.28.25%20Pictures%20for%20Posting/8-Groundbreaking.jpg",
  "https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/08.28.25%20Pictures%20for%20Posting/9-Groundbreaking.jpg",
  "https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/08.28.25%20Pictures%20for%20Posting/10-Groundbreaking.jpg",
  "https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/08.28.25%20Pictures%20for%20Posting/11-Groundbreaking.jpg",
  "https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/08.28.25%20Pictures%20for%20Posting/12-Groundbreaking.jpg",
]);

// Create slides with duplicates for infinite loop
const createSlides = () => {
  const slides = [];
  const totalRealSlides = Math.ceil(groundBreaking.value.length / imagesPerSlide);

  // Add last slide at the beginning (for seamless transition from first to last)
  const lastSlideStart = (totalRealSlides - 1) * imagesPerSlide;
  slides.push(groundBreaking.value.slice(lastSlideStart));

  // Add all real slides
  for (let i = 0; i < totalRealSlides; i++) {
    const start = i * imagesPerSlide;
    const end = start + imagesPerSlide;
    slides.push(groundBreaking.value.slice(start, end));
  }

  // Add first slide at the end (for seamless transition from last to first)
  slides.push(groundBreaking.value.slice(0, imagesPerSlide));

  return slides;
};

const allSlides = computed(() => createSlides());
const totalSlides = computed(() => Math.ceil(groundBreaking.value.length / imagesPerSlide));
const realSlideIndex = computed(() => currentSlide.value - 1); // For dot indicators

// Carousel navigation functions with seamless infinite loop
const nextSlide = () => {
  if (isTransitioning.value) return;

  isTransitioning.value = true;
  currentSlide.value++;

  // If we're at the duplicate first slide (at the end), jump to real first slide
  if (currentSlide.value > totalSlides.value) {
    setTimeout(() => {
      isTransitioning.value = false;
      currentSlide.value = 1; // Jump to real first slide without animation
    }, 300); // Match transition duration
  } else {
    setTimeout(() => {
      isTransitioning.value = false;
    }, 300);
  }
};

const prevSlide = () => {
  if (isTransitioning.value) return;

  isTransitioning.value = true;
  currentSlide.value--;

  // If we're at the duplicate last slide (at the beginning), jump to real last slide
  if (currentSlide.value < 1) {
    setTimeout(() => {
      isTransitioning.value = false;
      currentSlide.value = totalSlides.value; // Jump to real last slide without animation
    }, 300); // Match transition duration
  } else {
    setTimeout(() => {
      isTransitioning.value = false;
    }, 300);
  }
};

const goToSlide = (index) => {
  if (isTransitioning.value) return;
  currentSlide.value = index + 1; // +1 because we have duplicate at beginning
};

// Modal carousel functionality
const showModal = ref(false);
const modalCurrentIndex = ref(0);
const isFullScreen = ref(false);

const openImageModal = (image) => {
  // Find the index of the clicked image
  modalCurrentIndex.value = groundBreaking.value.findIndex(img => img === image);
  showModal.value = true;
  isFullScreen.value = false; // Reset full screen when opening

  // Prevent body scroll when modal is open
  document.body.style.overflow = 'hidden';
  document.body.classList.add('modal-open');

  // Add modal opening animation delay
  nextTick(() => {
    const modalElement = document.querySelector('.modal-overlay');
    if (modalElement) {
      modalElement.focus();
    }
  });
};

const closeModal = () => {
  isFullScreen.value = false; // Exit full screen if active
  showModal.value = false;
  modalCurrentIndex.value = 0;

  // Restore body scroll when modal is closed
  document.body.style.overflow = 'auto';
  document.body.classList.remove('modal-open');
};

// Modal carousel navigation
const nextModalImage = () => {
  modalCurrentIndex.value = (modalCurrentIndex.value + 1) % groundBreaking.value.length;
};

const prevModalImage = () => {
  modalCurrentIndex.value = modalCurrentIndex.value === 0
    ? groundBreaking.value.length - 1
    : modalCurrentIndex.value - 1;
};

const goToModalImage = (index) => {
  modalCurrentIndex.value = index;
};

// Full screen functionality
const toggleFullScreen = () => {
  isFullScreen.value = !isFullScreen.value;
};

const exitFullScreen = () => {
  isFullScreen.value = false;
};

// Keyboard navigation for modal
const handleKeydown = (event) => {
  if (!showModal.value) return;

  if (event.key === 'ArrowRight') {
    nextModalImage();
  } else if (event.key === 'ArrowLeft') {
    prevModalImage();
  } else if (event.key === 'Escape') {
    if (isFullScreen.value) {
      exitFullScreen();
    } else {
      closeModal();
    }
  } else if (event.key === 'f' || event.key === 'F') {
    toggleFullScreen();
  }
};

// Add keyboard event listener and cleanup
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
  // Ensure scroll is restored if component unmounts while modal is open
  if (showModal.value) {
    document.body.style.overflow = 'auto';
  }
});
</script>

<template>
  <div class="font-montserrat">
    <Header />
    <div v-if="display === 'desktop'">
      <Slider />
    </div>

    <div v-if="display === 'mobile'">
      <SliderMobile />
    </div>

    <div class="lg:relative block z-0">
      <div class="">
        <Shortcuts class="lg:-mt-0.5 bg-white 2xl-shadow" />
      </div>
      <div class="bg-[url('https://lsu-media-styles.sgp1.digitaloceanspaces.com/08.28.25%20Pictures%20for%20Posting-20250828T052746Z-1-001/unnamed.webp')] bg-cover bg-center bg-no-repeat   text-[#fff] py-10">
        <div
          class="w-11/12 mx-auto  text-center text-5xl mb-10 py-2"
        >
        <!--  -->
          <h2 class="text-shadow-lg/30 text-white  font-montserrat font-900 text-stroke-white tracking">
            What's New in LSU?</h2>
        </div>
        <div class="lg:flex items-center gap-x-10 w-11/12 mx-auto">
          <div class="w-7/12">
            <img
              src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/slides-landing-page/s1.jpg"
              class="shadow-2xl shadow-[#151817] -mt-5"
            />
          </div>
          <div class="w-full px-5 pt-3 pb-5  glass-effect">
            <div class="font-bold my-5 ">
              <p class="uppercase text-lg">A Milestone of Faith and Community</p>
            </div>

            <div class="text-[11px]">
              <p class="mb-2">
                After the BOT Meeting, the LSU community, led by Br. Rey Mejias
                FSC, LSU President, together with the Board of Trustees, and the
                Institutional Parents Auxiliary Board (IPAB), proudly gathered
                for the Groundbreaking of the Campus 1 & 2 Cafeteria — a
                heartfelt gift from our parents through IPAB.
              </p>

              <p class="mb-2">
                The celebration continued with the Blessing of Campus 2
                Completed Projects, which include the BEU Children’s Park, the
                newly repainted Blessed Virgin Mary (BVM) building, and the new
                volleyball court.
              </p>
              <p class="mb-2">
                This day marks not only the creation of new spaces but also the
                strengthening of our Lasallian spirit of collaboration,
                generosity, and shared mission. Animo La Salle!
              </p>
              <p>𝗕𝗲 𝗽𝗮𝗿𝘁 𝗼𝗳 𝘁𝗵𝗲 𝗟𝗮𝘀𝗮𝗹𝗹𝗶𝗮𝗻 𝗦𝘁𝗼𝗿𝘆!</p>
            </div>


             <div class="mt-10">
              <!-- Image Carousel -->
              <div class="relative">
                <!-- Carousel Container -->
                <div class="overflow-hidden rounded-lg">
                  <div class="flex ease-in-out"
                       :class="{ 'transition-transform duration-300': !isTransitioning || currentSlide !== 1 && currentSlide !== totalSlides }"
                       :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
                    <div v-for="(slide, slideIndex) in allSlides" :key="slideIndex"
                         class="w-full flex-shrink-0">
                      <div class="grid grid-cols-5 gap-2">
                        <div v-for="(image, imageIndex) in slide"
                             :key="imageIndex" class="relative group">
                          <img :src="image"
                               class="w-full h-[100px] object-cover border border-gray-300 rounded transition-transform duration-200 group-hover:scale-105 cursor-pointer"
                               @click="openImageModal(image)" />
                        </div>
                        <!-- Fill empty slots if needed -->
                        <div v-for="emptySlot in Math.max(0, imagesPerSlide - slide.length)"
                             :key="`empty-${emptySlot}`" class="w-full h-[100px]">
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Navigation Arrows -->
                <button @click="prevSlide"
                        class="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                  </svg>
                </button>

                <button @click="nextSlide"
                        class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>

                <!-- Dots Indicator -->
                <div class="flex justify-center mt-4 space-x-2">
                  <button v-for="index in totalSlides" :key="index"
                          @click="goToSlide(index - 1)"
                          class="w-3 h-3 rounded-full transition-all duration-200"
                          :class="currentSlide === index - 1 ? 'bg-white' : 'bg-white bg-opacity-50 hover:bg-opacity-75'">
                  </button>
                </div>
              </div>

              <!-- Image Modal Carousel -->
              <Teleport to="body">
                <div v-if="showModal"
                     class="fixed inset-0 bg-black flex items-center justify-center z-[9999] modal-overlay"
                     :class="isFullScreen ? 'bg-opacity-100' : 'bg-opacity-90'"
                     @click.self="isFullScreen ? exitFullScreen() : closeModal()"
                     style="backdrop-filter: blur(5px);">
                  <Transition
                    enter-active-class="transition-all duration-300 ease-out"
                    enter-from-class="opacity-0 scale-90"
                    enter-to-class="opacity-100 scale-100"
                    leave-active-class="transition-all duration-200 ease-in"
                    leave-from-class="opacity-100 scale-100"
                    leave-to-class="opacity-0 scale-90">
                    <div class="relative w-full h-full flex items-center justify-center modal-content"
                         :class="isFullScreen ? 'p-0' : 'p-4'"
                         @click.self="isFullScreen ? exitFullScreen() : closeModal()">

                  <!-- Main Image -->
                  <div class="relative"
                       :class="isFullScreen ? 'w-full h-full' : 'max-w-4xl max-h-full'">
                    <img :src="groundBreaking[modalCurrentIndex]"
                         class="object-contain shadow-2xl"
                         :class="isFullScreen ? 'w-full h-full cursor-pointer' : 'max-w-full max-h-full rounded-lg cursor-pointer'"
                         @click="toggleFullScreen" />

                    <!-- Image Counter (hidden in full screen) -->
                    <div v-if="!isFullScreen"
                         class="absolute top-4 left-4 bg-black bg-opacity-60 text-white px-3 py-1 rounded-full text-sm">
                      {{ modalCurrentIndex + 1 }} / {{ groundBreaking.length }}
                    </div>
                  </div>

                  <!-- Navigation Arrows (hidden in full screen) -->
                  <button v-if="!isFullScreen"
                          @click="prevModalImage"
                          class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-60 text-white p-3 rounded-full hover:bg-opacity-80 transition-all duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                  </button>

                  <button v-if="!isFullScreen"
                          @click="nextModalImage"
                          class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-60 text-white p-3 rounded-full hover:bg-opacity-80 transition-all duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </button>

                  <!-- Close Button (hidden in full screen) -->
                  <button v-if="!isFullScreen"
                          @click="closeModal"
                          class="absolute top-4 right-4 text-white bg-red-600 bg-opacity-80 rounded-full p-3 hover:bg-opacity-100 hover:scale-110 transition-all duration-200 z-10">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>

                  <!-- Full Screen Toggle Button -->
                  <button v-if="!isFullScreen"
                          @click="toggleFullScreen"
                          class="absolute top-4 right-16 text-white bg-blue-600 bg-opacity-80 rounded-full p-3 hover:bg-opacity-100 hover:scale-110 transition-all duration-200 z-10">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                    </svg>
                  </button>

                  <!-- Instructions (different for full screen and normal mode) -->
                  <div v-if="!isFullScreen"
                       class="absolute top-4 left-1/2 transform -translate-x-1/2 text-white text-sm bg-black bg-opacity-60 px-4 py-2 rounded-lg">
                    Click image or F for full screen • ESC to close
                  </div>

                  <!-- Full Screen Instructions (only visible in full screen) -->
                  <div v-if="isFullScreen"
                       class="absolute top-4 left-1/2 transform -translate-x-1/2 text-white text-sm bg-black bg-opacity-60 px-4 py-2 rounded-lg opacity-75">
                    Press ESC or click image to exit full screen • ← → to navigate
                  </div>

                  <!-- Thumbnail Navigation (hidden in full screen) -->
                  <div v-if="!isFullScreen"
                       class="absolute bottom-4 left-1/2 transform -translate-x-1/2 max-w-4xl">
                    <div class="flex space-x-2 overflow-x-auto pb-2 px-4">
                      <button v-for="(image, index) in groundBreaking" :key="index"
                              @click="goToModalImage(index)"
                              class="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200"
                              :class="modalCurrentIndex === index ? 'border-white' : 'border-transparent opacity-60 hover:opacity-80'">
                        <img :src="image"
                             class="w-full h-full object-cover" />
                      </button>
                    </div>
                  </div>

                      <!-- Keyboard Navigation Hint (hidden in full screen) -->
                      <div v-if="!isFullScreen"
                           class="absolute bottom-4 right-4 text-white text-sm bg-black bg-opacity-60 px-3 py-2 rounded-lg">
                        Use ← → keys or click thumbnails to navigate
                      </div>
                    </div>
                  </Transition>
                </div>
              </Teleport>
              
          </div>
          </div>
         
        </div>
      </div>
      <NewsAndUpdates />
    </div>

    <div class="pb-5">
      <div class="">
        <p
          class="w-7/12 text-center mx-auto uppercase font-bold lg:pt-0 pt-5 lg:mb-7 mb-5 lg:text-sm text-xs"
        >
          Affiliations
        </p>
        <div class="lg:w-5/12 w-11/12 mx-auto flex items-center">
          <a href="/" class="w-fit mx-auto" target="_blank">
            <img
              src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/Lasallian%20East%20Asia%20District.jpg"
              class="lg:w-16 w-10 mx-auto"
            />
          </a>

          <a href="/" class="w-fit mx-auto" target="_blank">
            <img
              src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/DLSP.jpg"
              class="lg:w-16 w-10 mx-auto"
            />
          </a>

          <a href="/" class="w-fit mx-auto" target="_blank">
            <img
              src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/CHED.png"
              class="lg:w-16 w-10 mx-auto"
            />
          </a>

          <a href="/" class="w-fit mx-auto" target="_blank">
            <img
              src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/DepEd.jpg"
              class="lg:w-16 w-10 mx-auto"
            />
          </a>
        </div>
      </div>
    </div>

    <div class="">
      <div class="lg:w-full w-11/12 mx-auto lg:pt-0 pt-3">
        <div class="lg:py-7 lg:flex lg:mt-0 mt-5">
          <div class="lg:w-9/12 lg:border-r border-green-200 px-2">
            <p
              class="w-11/12 mx-auto uppercase font-bold lg:mb-7 mb-5 lg:text-left text-center lg:text-sm text-xs"
            >
              Accreditations
            </p>
            <div
              class="grid lg:grid-cols-3 grid-cols-2 items-center mx-auto py-1 lg:gap-y-5 gap-y-5"
            >
              <a href="/" class="w-fit mx-auto" target="_blank">
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/PAASCU-Reaccredited.png"
                  class="lg:w-[60px] w-[50px] h-auto lg:mx-5 mx-auto"
                />
              </a>
              <a href="/" class="w-fit mx-auto" target="_blank">
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/LSU-ISO-Cert-Mark.jpg"
                  class="lg:w-[300px] w-[200px] h-auto lg:mx-5 mx-auto"
                />
              </a>
              <a href="/" class="w-fit mx-auto" target="_blank">
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/DPO-SEAL.png"
                  class="lg:w-[40px] w-[40px] h-auto object-contain lg:mx-5 mx-auto"
                />
              </a>
              <a href="/" class="w-fit mx-auto" target="_blank">
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/PCNC.png"
                  class="lg:w-[100px] w-[100px] h-auto lg:mx-5 mx-auto"
                />
              </a>

              <a href="/" class="w-fit mx-auto" target="_blank">
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/PEAC.png"
                  class="lg:w-[100px] w-[90px] h-auto lg:mx-5 mx-auto"
                />
              </a>
              <a href="/" class="w-fit mx-auto" target="_blank">
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/gradeA.png"
                  class="lg:w-[100px] w-[100px] h-auto lg:mx-5 mx-auto"
                />
              </a>
            </div>
          </div>
          <div class="lg:w-5/12 lg:px-10 lg:mt-0 mt-10 lg:mb-0 mb-4">
            <p
              class="uppercase font-bold lg:mb-7 mb-5 mx-auto lg:text-sm text-xs lg:text-left text-center"
            >
              Linkages
            </p>
            <div class="w-fit mx-auto py-1 lg:gap-y-7 gap-y-7">
              <div class="flex gap-x-10">
                <a href="/" class="w-fit mx-auto block" target="_blank">
                  <img
                    src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/partners/Philnitslogo.jpg"
                    class="lg:w-[100px] w-[80px] h-auto object-contain mx-auto"
                  />
                </a>
                <a
                  href="/linkages/Mikrotik"
                  class="w-fit mx-auto lg:mt-3 mt-1"
                  target="_blank"
                >
                  <img
                    src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/partners/MikrotikAcademy.jpg"
                    class="lg:w-[150px] w-[130px] h-auto object-contain lg:mx-5 mx-auto"
                /></a>
              </div>
              <div class="w-fit mx-auto lg:mt-10 mt-7">
                <a
                  href="/linkages/Mikrotik"
                  class="w-fit mx-auto"
                  target="_blank"
                >
                  <img
                    src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/GlobeBusiness.png"
                    class="lg:w-[100px] w-[100px] h-auto object-contain lg:mx-5 mx-auto"
                /></a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- <div class="mx-auto lg:w-6/12 lg:mb-5">
      <img
        src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/HEU%20Banner%202.png"
      />
    </div> -->

    <div class="bg-green-900 lg:py-10 py-3">
      <div>
        <p class="text-white font-bold text-lg text-center">
          LSU Tingog Campus Press
        </p>
        <p class="text-center lg:text-lg text-sm text-white">
          Handum, Tingog Photography Folio
        </p>
      </div>
      <div class="lg:px-10 lg:my-5 my-2">
        <iframe
          allowfullscreen
          allow="clipboard-write"
          scrolling="no"
          class="fp-iframe shadow-lg rounded-xl lg:h-screen w-full"
          src="https://heyzine.com/flip-book/5a43b14a6c.html#page/18"
        ></iframe>
      </div>
    </div>

    <Footer />
  </div>
</template>

<style scoped>
.bg {
  background: url("https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/green-tones-gradient-background_23-2148374436.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.glass-effect {
  /* Glassmorphism effect */
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow:
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);

  /* Floating effect */
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.glass-effect:hover {
  transform: translateY(-15px);
  box-shadow:
    0 12px 40px 0 rgba(31, 38, 135, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* Modal Styles */
.modal-overlay {
  animation: modalFadeIn 0.3s ease-out;
}

.modal-content {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(5px);
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Prevent body scroll when modal is open */
.modal-open {
  overflow: hidden;
}

/* Enhanced modal backdrop */
.modal-overlay::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at center, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.8) 100%);
  pointer-events: none;
}

/* Text stroke styles */
.font-peace-sans {
  font-family: 'Peace Sans', 'font-peace-sans', sans-serif;
}

.text-stroke-white {
  -webkit-text-stroke: 5px white;
  -webkit-text-fill-color: #ffffff; /* green-700 */
  paint-order: stroke fill;

}
</style>
