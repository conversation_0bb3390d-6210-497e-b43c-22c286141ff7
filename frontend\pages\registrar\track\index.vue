<script setup>
import { useUserStore } from "@/stores/user";
import moment from "moment";

const userStore = useUserStore();
const endpoint = ref(userStore.mainDevServer);
const listItems = ref([]);
const searchInput = ref("");
const filteredData = ref([]);
const notFoundAlert = ref(false);

onMounted(async () => {
  try {
    const result = await $fetch(endpoint.value + "/api/registrar/list");
    listItems.value = result || [];
  } catch (error) {
    console.error("Fetch error:", error);
  }
});

const trackNow = () => {
  const trackingID = searchInput.value.trim().toUpperCase();

  if (trackingID) {
    filteredData.value = listItems.value.filter(
      (item) => item?.tracking_id === trackingID
    );
    notFoundAlert.value = filteredData.value.length === 0;
  } else {
    filteredData.value = [];
    notFoundAlert.value = false;
  }
};
</script>

<template>
  <div class="bg-gray-50">
    <Header />

    <!-- Header Section -->
    <div class="relative">
      <div class="bg-green-700 lg:h-[200px] h-[130px]">
        <div
          class="lg:pt-10 absolute top-1/2 transform -translate-y-1/2 w-full"
        >
          <p
            class="font-bold uppercase text-white lg:text-2xl text-sm w-11/12 mx-auto"
          >
            REGISTRAR
          </p>
          <p class="text-xs w-11/12 mx-auto text-white">
            Higher Education Registrar Appointment
          </p>
        </div>
      </div>

      <!-- Nav Bar -->
      <div class="shadow-lg text-green-700">
        <div class="lg:flex justify-between border-b border-gray-200 lg:pl-5">
          <div
            class="flex items-center capitalize text-xs lg:border-b-0 border-b lg:px-0 px-1.5 py-2"
          >
            <div>
              <a href="/registrar" class="mr-2 hover:underline lg:h-10">Home</a>
            </div>
            <div>
              <i class="fas fa-caret-right"></i>
              <a
                href="/registrar/heu/appointment"
                class="mx-2 hover:underline lg:h-10"
                >HEU Appointment</a
              >
            </div>
          </div>
          <div class="flex hover:text-green-800 text-white bg-white h-full">
            <div
              class="hover:bg-green-800 border-x bg-white hover:text-white text-green-800 px-1 lg:px-4 lg:h-10 h-8 flex items-center capitalize text-xs lg:py-2 py-1 lg:w-fit w-full border-r"
            >
              <a href="/registrar" class="flex items-center w-fit mx-auto">
                <i class="fa fa-video-camera" aria-hidden="true"></i>
                <span class="ml-3 whitespace-nowrap">Demo Guide</span>
              </a>
            </div>
            <div
              class="hover:bg-green-800 border-x bg-white hover:text-white text-green-800 px-1 lg:px-4 lg:h-10 h-8 flex items-center capitalize text-xs lg:py-2 py-1 lg:w-fit w-full border-r"
            >
              <a
                href="/registrar/track"
                class="flex items-center w-fit mx-auto"
              >
                <i class="fa fa-universal-access" aria-hidden="true"></i>
                <span class="ml-3 whitespace-nowrap">Track</span>
              </a>
            </div>
            <div
              class="hover:bg-green-800 border-x bg-white hover:text-white text-green-800 px-1 lg:px-4 lg:h-10 h-8 flex items-center capitalize text-xs lg:py-2 py-1 lg:w-fit w-full"
            >
              <a
                href="/registrar/login"
                class="flex items-center w-fit mx-auto"
              >
                <i class="fa fa-user" aria-hidden="true"></i>
                <span class="ml-3 whitespace-nowrap">Admin Login</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="border-t-2 border-green-700 shadow-lg lg:my-5 my-3 lg:pb-10 w-11/12 mx-auto"
    >
      <h2
        class="lg:text-base text-xs px-10 uppercase py-2 font-bold bg-green-900 text-white text-center"
      >
        <i class="fa fa-certificate text-white mr-2"></i> Appointment Tracker
      </h2>

      <div class="lg:p-5 p-2">
        <div class="flex">
          <div
            class="flex lg:w-4/12 w-11/12 mx-auto my-3 text-center lg:rounded-lg rounded-md"
          >
            <div class="flex w-full">
              <div class="w-full">
                <input
                  class="py-1.5 pl-2 pr-2 uppercase w-full border-2 border-green-700 rounded-tl-md rounded-bl-md text-xs"
                  placeholder="Enter 20-digit Tracking ID"
                  v-model="searchInput"
                  maxlength="20"
                />
              </div>
              <div>
                <button
                  class="bg-green-700 hover:bg-green-800 text-white text-xs px-4 py-[7.7px] rounded-tr-md rounded-br-md"
                  @click="trackNow"
                >
                  Track
                </button>
              </div>
            </div>
          </div>
        </div>

        <div
          v-if="notFoundAlert"
          class="lg:text-sm text-xs text-red-800 text-center mt-5 py-2 px-4 rounded-sm shadow lg:w-6/12 mx-auto bg-red-100"
        >
          Can't find Tracking ID!
        </div>
      </div>

      <div v-if="filteredData.length > 0" class="px-4 py-2 lg:w-5/12 mx-auto">
        <div v-for="(item, i) in filteredData" :key="i">
          <div class="border-2 border-gray-300 shadow-lg mx-auto lg:w-11/12">
            <h2 class="lg:text-base text-xs px-10 uppercase py-2 font-bold text-green-900 text-center border-b bg-gray">
              Registrar's Appointment Form
            </h2>

            <div class="text-center my-2">
              <span class="text-xs block font-bold">
                Tracking ID: {{ item.tracking_id }}</span>
            </div>

            <div class="p-2">
              <div class="mb-2">
                <label class="font-semibold text-xs">Status:</label>
                <ul class="list-disc pl-5 text-sm">
                  <li v-for="(log, logIndex) in item.logs" :key="logIndex">
                    {{ log.timestamp }} — {{ log.status_remarks }}
                  </li>
                </ul>
              </div>

              <div class="bg-gray-100 px-5">
                <div class="mb-2">
                  <label class="font-semibold text-xs">Full Name:</label>
                  <div class="text-sm">
                    {{ item.firstname }} {{ item.middlename }} {{ item.lastname }}
                  </div>
                </div>

                <div class="mb-2">
                  <label class="font-semibold text-xs">College:</label>
                  <div class="text-sm">{{ item.college }}</div>
                </div>

                <div class="mb-2">
                  <label class="font-semibold text-xs">Course:</label>
                  <div class="text-sm">{{ item.course }}</div>
                </div>

                <div class="mb-2">
                  <label class="font-semibold text-xs">
                    Year Graduated / Last Attended:</label>
                  <div class="text-sm">
                    {{ item.year_graduated_last_attended }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <Footer />
  </div>
</template>

<style scoped>
input[type="radio"] {
  margin: 3px auto auto auto;
}
</style>
