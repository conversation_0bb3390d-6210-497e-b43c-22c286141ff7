<script setup>
</script>

<template>
  <div>
    <Header />
    <div class="bg-gray-50">
      <div class="">
        <div class="relative">
          <img
            src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/about.jpg"
            class="align-top w-full h-auto lg:object-fill lg:block hidden"
          />
          <img
            src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/green-tones-gradient-background_23-2148374436.png"
            class="align-top w-full h-36 object-none lg:hidden block"
          />
          <div class="pt-10 absolute top-1/2 transform -translate-y-1/2 w-full">
            <h1
              class="font-bold uppercase text-white lg:text-2xl text-lg w-11/12 mx-auto"
            >
              LIBRARY OVERVIEW
            </h1>
          </div>
          <div class="pt-2.5 pb-3 shadow-lg">
            <ul
              class="flex lasalle-green-text capitalize w-11/12 mx-auto text-xs"
            >
              <li>
                <a href="/" class="mr-1"> Home </a>
              </li>
              <li>
                <i class="fas fa-caret-right mr-1"></i>
                <a href="/library" class="mr-1">
                  Libraries and Media Centers
                </a>
              </li>
              <li>
                <i class="fas fa-caret-right mr-1"></i>
                <a href="/library/overview" class="mr-1"> Library Overview </a>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div class="pb-10">
        <div class="w-11/12 mx-auto pt-10 lasalle-green-text text-sm">
          <!-- <h1 class="lasalle-green-text font-bold text-2xl text-center">The Libraries and Media Center: An Overview</h1> -->
          <h2 class="font-bold lg:text-lg text-md">Brief Description</h2>
          <ol class="list-decimal lg:ml-10 ml-5">
            <li class="">
              <p class="lg:mb-3 mb-2">
                The LSU Libraries and Media Centers has
                <span class="font-bold">4 libraries</span> such as University
                Library, Senior High School Library, Junior High School Library
                and Grade School Library.
              </p>
              <p class="lg:mb-3 mb-2">
                The University Library serves Undergraduate and Graduate
                Students. The
                <span class="font-bold">Undergraduate</span> composed of 4
                colleges and 1 school such as College of Arts and Sciences,
                College of Business and Accountancy, College of Computer
                Science, Engineering and Architecture, College of Teacher
                Education, and School of Tourism and Hospitality Management
                while the <span class="font-bold">Graduate</span> serves the
                masters and doctorate students.
              </p>
              <p class="lg:mb-3 mb-2">
                The Grade School Library caters from Kinder to Grade 6 students,
                the Junior High School serves from the Grade 7 to Grade 10
                students. The Senior High School serves all students enrolled in
                all tracks.
              </p>
            </li>
            <li>
              <p>In this new normal, the library Opens 40 hours a week</p>
            </li>
            <li>
              <h1 class="font-bold">Collections:</h1>
              <ul class="list-disc lg:ml-10 ml-5">
                <li>
                  Book Accessions - 118,574vols (University Library); 1010vols
                  (Senior High School Library-Excluded that not yet accession);
                  23,584 vols (Junior High School Library); 17,974vols (Grade
                  School Library); and 3267vols (BMS Library)
                </li>
                <li>e-Books - 60,000 titles</li>
                <li>Print Periodicals - 16 titles</li>
                <li>
                  Online Database - 102 databases (approximately: 16,550+
                  journal titles)
                </li>
                <li>CD-ROM - 1,596 CDs</li>
                <li>Audio visuals - 2,318 Titles</li>
                <li>
                  Theses &amp; dissertations - 2,909 titles (University Library)
                </li>
                <li>Other print materials - 30,000+ vols</li>
                <li>Maps - 172 pieces</li>
                <li>Artworks - 386+ pieces</li>
              </ul>
            </li>
            <li>
              <h1>
                <span class="font-bold">Entrants</span> (based on T1, AY
                2019-2020)
              </h1>
              <ul class="list-disc lg:ml-10 ml-5">
                <li>
                  Entrants TLC -
                  <span class="font-bold lasalle-green-text">250</span> users
                  ave./day
                </li>
              </ul>
            </li>
            <li>
              <h1>
                <span class="font-bold">Resource utilization</span>
                (based on T1, AY 2019-2020)
              </h1>
              <ul class="list-disc lg:ml-10 ml-5">
                <li>Book loans - 16 vols ave./day</li>
                <li>Room use - 16 vols ave./day</li>
                <li>Books return from book drop - (New Service)</li>
                <li>CyberSpace - 2,099 users ave./month</li>
                <li>Graduate corner - 1 user ave./day</li>
                <li>Faculty corner - 5 users ave./month</li>
                <li>AV multimedia rooms - 2 classes ave./day</li>
                <li>Discussion rooms - 1 groups ave./day</li>
                <li>Online databases - 1,276 downloads ave./mo.</li>
              </ul>
            </li>
            <li>
              <h1 class="font-bold">Facilities</h1>
              <ul class="list-disc lg:ml-10 ml-5">
                <li>The University Library, 1st, 2nd and 3rd Floor</li>
                <li>LSU Integrated School Libraries, 3 libraries</li>
                <li>Public Computer Terminal - 8 units</li>
                <li>AV rooms - 3</li>
                <li>CyberCafe - 1</li>
                <li>Discussion Room - 4</li>
                <li>Quiet Room - 1</li>
                <li>Exhibit Area, 3rd Floor - 1</li>
                <li>Book Drop - 2 sites (one per gate)</li>
                <li>Book Thru - 2 sites (one per gate)</li>
                <li>
                  Integrated library system - Follett Destiny Library Manager
                  Release Date : (March 2005)
                </li>
                <li>WiFi access - all floors</li>
              </ul>
            </li>
            <li>
              <h1 class="font-bold">Potential user</h1>
              <ul class="list-disc lg:ml-10 ml-5">
                <li>
                  Faculty - 139+ average /term (College) / 117+ average/term
                  (Basic Ed)
                </li>
                <li>Undergraduate - 3,000+ average/term</li>
                <li>Graduate - 250+ average/term</li>
                <li>Senior High School - 1,036+ average/term</li>
                <li>Junior High School - 549+ average/year</li>
                <li>Grade School - 287+ average/year</li>
                <li>Brother Martin Simpson - 380+ average/year</li>
                <li>Special Education - 40+ average/year</li>
              </ul>
            </li>
            <li>
              <h1><span class="font-bold">Personnel</span> - 6</h1>
              <ul class="list-disc lg:ml-10 ml-5">
                <li>Director - 1</li>
                <li>Librarian - 4</li>
                <li>Library Clerk - 1</li>
              </ul>
            </li>
          </ol>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>
  
<style scoped>
@media only screen and (max-width: 1023px) {
  .sub-header {
    background: #087830;
  }
}
@media only screen and (max-width: 2560px) {
  .sub-header {
    height: 170px;
  }
}
@media only screen and (max-width: 1440px) {
  .sub-header {
    height: 170px;
  }
}
@media only screen and (max-width: 1024px) {
  .sub-header {
    height: 170px;
  }
}
@media only screen and (max-width: 768px) {
  .sub-header {
    height: 170px;
  }
}
@media only screen and (max-width: 425px) {
  .sub-header {
    height: 170px;
  }
}
@media only screen and (max-width: 375px) {
  .sub-header {
    height: 170px;
  }
}
@media only screen and (max-width: 320px) {
  .sub-header {
    height: 170px;
  }
}
</style>