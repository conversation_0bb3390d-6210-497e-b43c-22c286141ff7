<template>
  <div class="landing-page">
    <!-- Skip link for accessibility -->
    <ul class="skip-links">
      <li>
        <a href="#main" ref="skipLink" class="skip-link">Skip to main content</a>
      </li>
    </ul>

    <!-- Header/Navigation -->
    <header class="site-header">
      <div class="top-nav">
        <div class="container">
          <nav class="utility-nav">
            <ul>
              <li><a href="#">LSU NEWS</a></li>
              <li><a href="#">ATHLETICS</a></li>
              <li><a href="#">VISIT</a></li>
              <li><a href="#">EVENTS</a></li>
              <li><a href="#">ALUMNI</a></li>
              <li><a href="#">GIVE</a></li>
            </ul>
          </nav>
          <div class="search-icon">
            <button aria-label="Search">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
      </div>
      
      <div class="main-nav-container">
        <div class="container">
          <div class="logo">
            <a href="/">
              <img src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/logos/lsu-w-h.png" alt="La Salle University Logo">
            </a>
          </div>
          
          <nav class="main-nav">
            <ul>
              <li class="dropdown">
                <a href="#">ACADEMICS & ADMISSIONS <i class="fas fa-chevron-down"></i></a>
              </li>
              <li class="dropdown">
                <a href="#">CAMPUS <i class="fas fa-chevron-down"></i></a>
              </li>
              <li class="dropdown">
                <a href="#">RESOURCES <i class="fas fa-chevron-down"></i></a>
              </li>
              <li class="dropdown">
                <a href="#">ABILITIES <i class="fas fa-chevron-down"></i></a>
              </li>
              <li class="dropdown">
                <a href="#">IGNITERS <i class="fas fa-chevron-down"></i></a>
              </li>
            </ul>
          </nav>
          
          <div class="cta-buttons">
            <a href="#" class="btn btn-info">REQUEST INFO</a>
            <a href="#" class="btn btn-apply">APPLY</a>
          </div>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <main id="main" role="main">
      <section class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">
            GET READY
            <span class="hero-subtitle">TO IGNITE YOUR POTENTIAL</span>
          </h1>
          <div class="hero-buttons">
            <a href="#" class="btn btn-primary">FIND YOUR PROGRAM</a>
            <a href="#" class="btn btn-secondary">WHY LSU</a>
            <a href="#" class="btn btn-primary btn-apply-now">APPLY NOW</a>
          </div>
        </div>
      </section>
    </main>

    <!-- Chatbot -->
    <div class="chatbot">
      <div class="chat-bubble">
        <p>Hi there! I'm here to help you find what you're looking for.</p>
      </div>
      <button class="chat-icon" aria-label="Chat with us">
        <img src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/icons/chat-icon.png" alt="Chat icon">
      </button>
      <button class="accessibility-icon" aria-label="Accessibility options">
        <i class="fas fa-universal-access"></i>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const skipLink = ref(null);

// Focus on skip link when route changes for accessibility
watch(
  () => route.path,
  () => {
    skipLink.value?.focus();
  }
);
</script>

<style scoped>
/* Base Styles */
.landing-page {
  font-family: 'Arial', sans-serif;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background: #008028;
  color: white;
  padding: 8px;
  z-index: 100;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 0;
}

.skip-links {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Header Styles */
.site-header {
  position: relative;
  z-index: 10;
}

.top-nav {
  background-color: #008028;
  color: white;
  padding: 8px 0;
}

.utility-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 20px;
}

.utility-nav a {
  color: white;
  text-decoration: none;
  font-size: 0.8rem;
  font-weight: bold;
}

.utility-nav a:hover {
  text-decoration: underline;
}

.search-icon button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1rem;
}

.main-nav-container {
  background-color: white;
  padding: 15px 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.logo img {
  height: 50px;
}

.main-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 25px;
}

.main-nav a {
  color: #333;
  text-decoration: none;
  font-weight: bold;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.main-nav a i {
  margin-left: 5px;
  font-size: 0.7rem;
}

.dropdown:hover > a {
  color: #008028;
}

.cta-buttons {
  display: flex;
  gap: 10px;
}

.btn {
  display: inline-block;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: bold;
  text-align: center;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-info {
  background-color: #ffc107;
  color: #333;
}

.btn-apply {
  background-color: #ffc107;
  color: #333;
}

.btn-info:hover, .btn-apply:hover {
  background-color: #e0a800;
}

/* Hero Section */
.hero-section {
  background-image: url('https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/alverno-students.jpg');
  background-size: cover;
  background-position: center;
  height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
}

.hero-content {
  max-width: 800px;
  padding: 0 20px;
}

.hero-title {
  font-size: 6rem;
  font-weight: 800;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1;
  margin-bottom: 30px;
}

.hero-subtitle {
  display: block;
  font-size: 3rem;
  margin-top: 10px;
}

.hero-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.btn-primary {
  background-color: #008028;
  color: white;
}

.btn-secondary {
  background-color: white;
  color: #008028;
}

.btn-primary:hover {
  background-color: #006820;
}

.btn-secondary:hover {
  background-color: #f0f0f0;
}

/* Chatbot */
.chatbot {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  align-items: flex-end;
  z-index: 100;
}

.chat-bubble {
  background-color: white;
  border-radius: 10px;
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-right: 10px;
  max-width: 250px;
}

.chat-bubble p {
  margin: 0;
  font-size: 0.9rem;
}

.chat-icon, .accessibility-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.chat-icon {
  background-color: #008028;
}

.chat-icon img {
  width: 30px;
  height: 30px;
}

.accessibility-icon {
  background-color: #008028;
  color: white;
  font-size: 1.5rem;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .hero-title {
    font-size: 5rem;
  }
  
  .hero-subtitle {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .main-nav-container .container {
    padding: 15px;
  }
  
  .logo {
    margin-bottom: 15px;
  }
  
  .main-nav ul {
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
  }
  
  .utility-nav ul {
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .hero-title {
    font-size: 3.5rem;
  }
  
  .hero-subtitle {
    font-size: 2rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .chat-bubble {
    display: none;
  }
}
</style>

