<script setup>
import _ from "lodash";
let search = ref("");

// const tracksLists = await $fetch(endpoint.value + "/api/appointments/tracking/list/").catch((error) => error.data)
// const bookingAppointmentList = await $fetch(enpoint.value + "/api/appointments/list/").catch((error) => error.data)

// const registrar = await $fetch('https://registrar.lsu.edu.ph').catch((error) => error.data)

// console.log(registrar)



</script>

<template>
  <div class="bg-gray-50">
    <Header />
    <div class="">
      <div class="">
        <div class="relative">
          <img
            src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/about.jpg"
            class="align-top w-full h-auto lg:object-fill lg:block hidden"
          />
          <img
            src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/green-tones-gradient-background_23-2148374436.png"
            class="align-top w-full h-36 object-none lg:hidden block"
          />
          <div class="pt-10 absolute top-1/2 transform -translate-y-1/2 w-full">
            <h1
              class="font-bold uppercase text-white lg:text-2xl text-lg w-11/12 mx-auto"
            >
              Track Appointment Status
            </h1>
          </div>
          <div class="pt-2.5 pb-3 shadow-lg">
            <ul class="flex lasalle-green-text capitalize w-11/12 mx-auto text-xs">
              <li>
                <a href="/" class="mr-1"> Home </a>
              </li>
              <li>
                <i class="fas fa-caret-right mr-1"></i>
                <a href="/registrar" class="mr-1">University Registrar</a>
              </li>
              <li>
                <i class="fas fa-caret-right mr-1"></i>
                <a href="/" class="mr-1">Track Appointment Status</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="mt-5 w-11/12 mx-auto pb-7 bg-white mb-5 shadow-md">
        <div class="mx-auto w-fit pt-10 pb-5 lg:flex items-center">
          <label class="lasalle-green-text mr-3 whitespace-nowrap"
            >Enter Reference Code:</label
          >
          <input
            v-model="search"
            class="text-center w-7/12 shadow-md px-1 py-1 rounded-md border-y border-x-2 border-green-800"
            placeholder="e.g APS1683827018720"
            name="search"
            maxlength="16"
          />
        </div>
        <div
          :class="b.referencecode === search ? 'block' : 'hidden'"
          v-for="(b, i) in bookingAppointmentList"
          :key="i"
        >
          <div class="w-full items-center lg:flex lg:mb-3 mt-5">
            <ul class="lg:flex w-11/12 mx-auto justify-center">
              <li class="relative -ml-1">
                <span class="block">
                  <i
                    class="fa fa-check-circle text-4xl"
                    :class="b.successful_request ? 'text-green-800' : 'text-gray-300'"
                    aria-hidden="true"
                  ></i>
                </span>
                <span
                  class="block absolute lg:-left-12 whitespace-nowrap"
                  :class="b.successful_request ? 'text-green-800' : 'text-gray-300'"
                  >Successful Request</span
                >
              </li>
              <li
                class="-ml-1 lg:w-56 flex items-center h-3 my-auto"
                :class="b.successful_request ? 'bg-green-800' : 'bg-gray-300'"
              >
                <span
                  class="h-3 w-0/12"
                  :class="b.successful_request ? 'bg-green-800' : 'bg-gray-300'"
                ></span>
              </li>

              <li class="relative -ml-1">
                <span class="block">
                  <i
                    class="fa fa-check-circle text-4xl"
                    :class="b.appointment_confirm ? 'text-green-800' : 'text-gray-300'"
                    aria-hidden="true"
                  ></i>
                </span>
                <span
                  class="block absolute -left-12 whitespace-nowrap"
                  :class="b.appointment_confirm ? 'text-green-800' : 'text-gray-300'"
                  >Appointment Confirmed</span
                >
              </li>
              <li
                class="-ml-1 lg:w-56 flex items-center h-3 my-auto"
                :class="b.appointment_confirm ? 'bg-green-800' : 'bg-gray-300'"
              >
                <span
                  class="h-3 w-0/12"
                  :class="b.appointment_confirm ? 'bg-green-800' : 'bg-gray-300'"
                ></span>
              </li>

              <li class="relative -ml-1">
                <span class="block">
                  <i
                    class="fa fa-check-circle text-4xl"
                    :class="b.payment ? 'text-green-800' : 'text-gray-300'"
                    aria-hidden="true"
                  ></i>
                </span>
                <span
                  class="block absolute -left-12 whitespace-nowrap"
                  :class="b.payment ? 'text-green-800' : 'text-gray-300'"
                  >Payment</span
                >
              </li>
              <li
                class="-ml-1 lg:w-56 flex items-center h-3 my-auto"
                :class="b.payment ? 'bg-green-800' : 'bg-gray-300'"
              >
                <span
                  class="h-3 w-0/12"
                  :class="b.payment ? 'bg-green-800' : 'bg-gray-300'"
                ></span>
              </li>

              <li class="relative -ml-1">
                <span class="block">
                  <i
                    class="fa fa-check-circle text-4xl"
                    :class="b.request_delivered ? 'text-green-800' : 'text-gray-300'"
                    aria-hidden="true"
                  ></i>
                </span>
                <span
                  class="block absolute -left-12 whitespace-nowrap"
                  :class="b.request_delivered ? 'text-green-800' : 'text-gray-300'"
                  >Request Delivered</span
                >
              </li>
            </ul>
          </div>
        </div>

        <div class="mx-auto w-fit mb-10 mt-10">
          <ol
            class="relative border-l border-gray-200 dark:border-gray-700"
            :class="t.referencecode === search ? 'block' : 'hidden'"
            v-for="(t, i) in _.orderBy(tracksLists, 'date', 'time', 'asc')"
            :key="i"
          >
            <li class="pt-10 ml-4">
              <div
                class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-900 dark:bg-gray-700"
              ></div>
              <p
                class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500"
              >
                {{ t.date }}
              </p>
              <p
                class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500"
              >
                {{ t.time }}
              </p>
              <p
                class="pb-2 text-base font-normal w-10/12 text-gray-500 dark:text-gray-400"
              >
                {{ t.description }}
              </p>
            </li>
          </ol>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<style scoped></style>



<script setup>
import _ from "lodash";

import moment from "moment";



let search = ref("");

const tbltrackorder = await $fetch(
  "https://api-registrar.lsu.edu.ph/api/appointments/list/tbltrackorder/"
).catch((error) => error.data);
const tblorders = await $fetch(
  "https://api-registrar.lsu.edu.ph/api/appointments/list/tblorders/"
).catch((error) => error.data);
const tbltransactions = await $fetch(
  "https://api-registrar.lsu.edu.ph/api/appointments/list/tbltransactions/"
).catch((error) => error.data);

//console.log(tbltrackorder);
//console.log(tblorders);
//console.log(tbltransactions);




const filterSearch = () => {
 tbltrackorder.filter(function (params) {
      if (params.fldorderid === search.value) {
      
      }
    });
}
</script>

<template>
  <div class="bg-gray-50">
    <Header />
    <div class="">
      <div class="">
        <div class="relative">
          <img
            src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/about.jpg"
            class="align-top w-full h-auto lg:object-fill lg:block hidden"
          />
          <img
            src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/green-tones-gradient-background_23-2148374436.png"
            class="align-top w-full h-36 object-none lg:hidden block"
          />
          <div class="pt-10 absolute top-1/2 transform -translate-y-1/2 w-full">
            <h1
              class="font-bold uppercase text-white lg:text-2xl text-lg w-11/12 mx-auto"
            >
              Track Appointment Status
            </h1>
          </div>
          <div class="pt-2.5 pb-3 shadow-lg">
            <ul class="flex lasalle-green-text capitalize w-11/12 mx-auto text-xs">
              <li>
                <a href="/" class="mr-1"> Home </a>
              </li>
              <li>
                <i class="fas fa-caret-right mr-1"></i>
                <a href="/registrar" class="mr-1">University Registrar</a>
              </li>
              <li>
                <i class="fas fa-caret-right mr-1"></i>
                <a href="/" class="mr-1">Track Appointment Status</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="mt-5 w-11/12 mx-auto pb-7 bg-white mb-5 shadow-md">
        <div class="mx-auto w-fit pt-10 pb-5 lg:flex items-center">
          <label class="lasalle-green-text mr-3 whitespace-nowrap">
            Tracking Code:</label>
          <input
            v-model="search"
            class="text-center w-7/12 shadow-md px-1 py-1 rounded-md border-y border-x-2 border-green-800"
            placeholder="Tracking Code"
            name="search"

          />
             <!-- maxlength="16" -->
          <button @click="filterSearch">Go</button>
        </div>

<div>

        <div>
          <div class="text-center font-bold text-green-900">Appointment Status</div>
          <ul class="w-[600px] mx-auto">
            <!-- <div>
              453589347589785748759834758
            </div> -->
            <li :key="i" v-for="(j, i) in filterSearch" class="">
              <!-- <div class="font-bold text-sm">
                Document
              </div>
              <div class="">
                Status - Date</div> -->
                <!-- <div class="block">{{j.fldorderid}}</div> -->
                  <div class="block font-bold">Sample Document</div>
                  <!-- {{j.fldusername}} -->
                  <div class="">
                    <div class="block ml-20">
                      <i class="fa fa-circle mr-3 text-green-950" aria-hidden="true"></i>
                      <span class="italic">{{ j.flddescription }}</span> -
                      {{ moment(j.flddatetime).format("MM-DD-YYYY") }}<br />
                    </div>
                  </div>
                  <div class="border-l-2 h-[40px] ml-[87px] -mt-3 pl-5 pt-2 pb-3">
                    Description
                  </div>
              <!-- {{j.flddocid}} -->
            </li>
          </ul>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<style scoped></style>