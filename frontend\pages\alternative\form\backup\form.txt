        <form v-on:submit.prevent="submitForm" class="px-3 flex flex-wrap">
          <div>
            <input type="text" 
              v-model="newStudent.id"
              class="" />
            <input type="text" 
              v-model="newStudent.confirmationID" />
            <input type="text" 
              v-model="newStudent.temporaryIDNumber" />
          </div>
         
         
          <div>
          </div>
          <div class="w-full lg:mb-0 mb-2">
            <label class="text-[10px] text-gray-300 lg:block hidden"> documents </label>
            <div class="mt-3">
              <div class="flex w-full mb-3">
                <div class="flex h-fit">
                  <!-- accept="image/png, image/jpeg" -->
                  <input type="file" @change="uploadedFile" multiple />
                </div>
              </div>
              <div class="w-fit lg:mx-0 mx-auto">
                <div class="w-56 shadow">
                  <img class="lg:w-full h-56 object-contain" :src="imagePreview" />
                </div>
              </div>
            </div>
          </div>
          <div class="w-full">
            <button class="w-fit mx-auto block">Submit</button>
          </div>
        </form>
         <!-- <img :src="admissionsMediaPathUrl + imageFile" /> -->