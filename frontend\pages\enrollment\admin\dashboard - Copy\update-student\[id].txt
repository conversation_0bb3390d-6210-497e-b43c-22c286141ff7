<script setup>
import { onMounted, ref } from "vue";
import "@vuepic/vue-datepicker/dist/main.css";
import { useUserStore } from "@/stores/user";
const router = useRouter();
const userStore = useUserStore();
const route = useRoute();
import _ from "lodash";
const endpoint = ref(userStore.mainDevServer);
import VueDatePicker from "@vuepic/vue-datepicker";
import moment from "moment";
import courseOrProgramJSON from "./courseProgram.json";
import collegeOrSchoolJSON from "./college_school.json";


const collegeOrSchoolData = ref(collegeOrSchoolJSON);
const courseOrProgramData = ref(courseOrProgramJSON);

const id = route.params.id
let singleEnrollee = await $fetch(endpoint.value + "/api/admissions/" + route.params.id + "/").catch((error) => error.data)
const evaluationWithData = ref();



const studentPersonalDetailsToggle = ref(false);
const studentContactInformationToggle = ref(false);
const studentAlienStatusInformationToggle = ref(false);
const evaluationTab = ref(false);

const spinner = ref(false);

const ageRange = ref([
    '18', '19', '20', '21', '22', '23', '24', '25',
    '26', '27', '28', '29', '30', '31', '32', '33', 
    '34', '35', '36', '37', '38', '39', '40', '41',
    '42', '43', '44', '45', '46', '47', '48', '49', '50'
])

const birthOrderList = ref([
    '1st', '2nd', '3rd', '4th', '5th', 'Nth'
])

const civilStatusList = ref([
    'Single',
    'Married',
    'Separated',
    'Divorced',
    'Widowed'
])

const citizenshipList = ref([
    'filipino',
    'non-filipino'
])

const languagesSpokenList = ref([
    'Tagalog',
    'English',
    'Bisaya or Cebuano'
])

const evaluationStatic = ref({
  heading: {
    question: 'Check out the continuum below and assess your level of satisfaction during the online enrollment process in terms of:',
    score: [
      {
        number: 5,
        text: 'Completely Satisfied'
      },
      {
        number: 4,
        text: 'Very Satisfied'
      },
      {
        number: 3,
        text: 'Moderately Satisfied'
      },
      {
        number: 2,
        text: 'Slightly Satisfied'
      },
      {
        number: 1,
        text: 'Not at all Satisfied'
      },
      {
        number: 0,
        text: 'Not Applicable'
      }
    ]
  }
})

const staticNewStudentTexts = ref({
  data_privacy_notice_consent: {
    text: [
      'Data Privacy Notice',
      'La Salle University Ozamiz upholds the Republic Act No. 10173 known as "Data Privacy Act of 2012" which declares the policy of the State to protect the fundamental human right of privacy while ensuring free flow of information to promote innovation and growth.',
      'In line with this, we would like to have your consent to collect and process all relevant personal information. This may include the student’s personal identifiable information such as name, age, sex at birth, address, contact details, and other relevant (previous) school records, that are relevant for LSU Admissions and Scholarships Center processes. This may include endorsement of student data (if requested) to other Lasallian Schools, DLSP, CHED, Dep-Ed and other legitimate (government/non-government) agencies. Rest assured that information you share with us will be treated with utmost confidentiality.',
      'If you agree to the abovementioned, kindly click the agree button below siginfying your consent and willingness to participate in the process of the collection, use and release of your information to the authorized personnel for legitimate purposes stated above.',
      'Should you wish to withdraw your consent, please get in touch with us through our website www.lsu.edu.ph or email <NAME_EMAIL>.',
      'I have READ and UNDERSTOOD the above statements and AGREE to all its terms and conditions',
      'I am 18 years old or above'
    ],
  },
  has_health_condition: {
    text: [
      'Support for Students with Health Conditions:',
      'Sharing the current state of your health will provide relevant information for us to understand your needs, and help us provide the necessary support for you to succeed at the University, and in life. The University, through the Career and Counseling Center (CCC) would like to provide further support by asking if you have needs due to any of the following disabilities consistent with the pertinent definitions used in the Implementing Rules and Regulations of RA 9442 and RA 7277 (otherwise known as the Magna Carta for Disabled Persons and for other Purposes)',
      '(1) Psychosocial and behavioral disability, (2) Chronic illness with disability, (3) Learning (cognitive or intellectual) disability, (4) Mental disability, (5) Visual/seeing disability, (6) Orthopedic/moving, and (7) Communication disability.',
      'Depending on the outcome of the initial interview, a confidential meeting with the appropriate school members may be arranged in order to discuss possible support for you.',
      'PLEASE NOTE THAT YOU MAY CHOOSE TO SKIP THIS ITEM. THE UNIVERSITY RESPECT EACH LEARNER S DESIRE FOR CONFIDENTIALITY.',
      'I have a health condition and I am interested in the student support service(s) offered by the University.'
    ],
  },
  hereby_certification: {
    text: [
      'By clicking the box below, I hereby:',
      'Certify that all information  written in this application is complete and accurate. If accepted as  a student, I agree that my admission, registration, and graduation are subject to  the rules and regulations of La Salle University - Ozamiz.',
      'I have READ, UNDERSTOOD  the above statements and AGREE to all its terms and conditions. I understand that checking of the box shall be interpreted as constituting a signature of certification of the above-mentioned. '
    ],
  },
  media_release_consent: {
    text: [
      'By clicking the AGREE box below,',
      'I hereby:',
      'Give my consent to LSU the right to take and use photographs/film of me in all forms, media, and manner, in conjunction with my name or anonymize the data, for advertising, trade, promotion, exhibition, in the pursuit of promoting the projects and activities of LSU.',
      'Examples of LSU school-related activities/events and publications include but are not limited to:',
      'Subject to my right to withdraw and cancel my consent and permission as provided below, I waive any right to review, approve or disapprove the use of the photographs/films/videos for the promotion of the projects and activities of LSU, now and in the future, regardless of whether that use of manner is known to me.',
      'I consent to the provisions of this form and acknowledge the ownership by the LSU of the contents to be uploaded to the official websites, family, student, and employee portals, as well as official social media accounts of the LSU and Lasallian Formation and Mission Department s affiliated social media accounts.',
      'I understand that I will not be given to the media/press or public without my prior consent. I also understand that I have the right to withdraw and cancel my consent and permission at any time by notice in <NAME_EMAIL>. Beginning from the date of my withdrawal of consent, LSU will take all reasonable steps to ensure that the photographs/films/videos of my child will no longer be used for the promotion of LSU projects and activities.',
      'I agree to use any media containing information of other persons, such as photos and videos during School event/s, for purely personal uses and purposes, with no connection to any professional, business, official, or commercial activity, and in compliance with the Data Privacy Act of the Philippines and any of its amendments and implementing rules and regulations.',
      'I agree to use any media containing information of other persons, such as photos and videos during School event/s, for purely personal uses and purposes, with no connection to any professional, business, official, or commercial activity, and in compliance with the Data Privacy Act of the Philippines and any of its amendments and implementing rules and regulations.',
      'By providing LSU your permission, the student’s names and/or likenesses, photos, video, and/or audio may be used in LSU school-related publication in print, analog or digital media.',
      'I have READ, UNDERSTOOD  the above statements and to all its terms and conditions. I understand that checking of the box shall be interpreted as constituting a signature of consent of the above-mentioned.'
    ],
    list: [
      'Audio and video recordings for cultural, formation, spiritual, and sports events such as PEP Rally and Vision Mission Week, or school events',
      'Officially recognized activities and events the Lasallian Mission and Office of the University Chancellor, student clubs, and student organizations',
      'Seminars-Workshops, Talks, and Training,',
      'Retreat, Recollection, Outreach/In-Reach, Service Learning, and Volunteer Activities',
      'Awards Ceremonies',
      'Cultural Production like concerts and plays',
      'Sports activity programs or sheets, such as for basketball, showing the weight and height of students',
      'Student Publication',
      'Name and picture in the LSU yearbook',
      'Honor roll and other recognition lists and programs',
      'Graduation programs and announcements',
      'Student photographs for classroom, teacher, or reports for school use',
      'Graduation Ball/Party',
      'Marketing Brochures',
      'Websites',
      'LSU Official Social Media Accounts Posts',
      'Videos for internal or external use, promotional media (local and national news), and other marketing and communication materials',
    ],
  },
})

const filterEvaluation = async () => {
  let evaluationFetch = await $fetch(endpoint.value + "/api/admissions/submit-evaluation-form/list/").catch((error) => error.data)
  //console.log('evaluationFetch', evaluationFetch)

  evaluationFetch.filter(function (params) {
    if (singleEnrollee.tracking_id === params.tracking_id) {
      evaluationWithData.value = params.evaluation_form
    } else { }
  })
  evaluationTab.value = !evaluationTab.value;
}

const displaySecondaryContactAddress = ref(singleEnrollee.student_contact_info.the_same_address.answer);

onMounted(() => {
  if (
    userStore.user.isAuthenticated &&
    (
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>' ||
      userStore.user.email === '<EMAIL>'
    )
  ) {
    router.push("/enrollment/admin/dashboard/update-student/" + route.params.id);
    // console.log(singleEnrollee)
  } else {
    router.push("/unauthorized");
  }
});



const theSameAddressBtn = (choice) => {
  if (choice === 'yesStudentContactInfo') {
      singleEnrollee.student_contact_info.the_same_address.answer = 'yes'
      singleEnrollee.student_contact_info.current_or_present_living_home_address_category = singleEnrollee.student_contact_info.permanent_living_home_address_category
      displaySecondaryContactAddress.value = 'yes';
      //console.log(singleEnrollee.value)
  }
  if (choice === 'noStudentContactInfo') {
      singleEnrollee.student_contact_info.the_same_address.answer = 'no'
      displaySecondaryContactAddress.value = 'no';
      singleEnrollee.student_contact_info.current_or_present_living_home_address_category = {}
  }
}



const backToMainPortalPage = async () => {
  singleEnrollee = null
  router.push("/enrollment/admin/dashboard/");
}

const logOut = () => {
  router.push("/enrollment");
  singleEnrollee = null
  userStore.removeToken();
}



const newStudentSubmitToEmail = ref({
  tracking_id: singleEnrollee.tracking_id,
  student_lsu_id_number: singleEnrollee.student_lsu_id_number,
  firstname: singleEnrollee.student_personal_info.firstname,
  email: singleEnrollee.student_contact_info.contact.personal_email_address,
  lsu_email: singleEnrollee.student_contact_info.contact.lsu_email_address,
})


const editStatus = async () => {
  spinner.value = true
  await $fetch(endpoint.value + "/api/admissions/" + route.params.id + "/edit/", {
    method: "PUT",
    body: singleEnrollee,
  }).then((response) => {
    //console.log("response", response);
    router.push("/enrollment/admin/dashboard/");
    spinner.value = false
  })
}


const admissionDone = async () => {
  spinner.value = true
  await $fetch(endpoint.value + "/api/admissions/submit-new-student-to-gmail-admissions-done/", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: newStudentSubmitToEmail.value,
  }).then((response) => {
    //console.log(response);
    editStatus()
    router.push("/enrollment/admin/dashboard/");
    spinner.value = false
  })
}


const advisingDone = async () => {
  spinner.value = true
  await $fetch(endpoint.value + "/api/admissions/submit-new-student-to-gmail-advising-done/", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: newStudentSubmitToEmail.value,
  }).then((response) => {
    //console.log(response);
    editStatus()
    router.push("/enrollment/admin/dashboard/");
    spinner.value = false
  })
}

const accountingDone = async () => {
  spinner.value = true
  singleEnrollee.receipt.confirm = true
  singleEnrollee.enrollment_tracking_status[2] = {
    track_name: 'accounting',
    label: 'payment',
    details: 'payment',
    date_checked: '-',
    check_by: '-',
    status: 'yes',
    remarks: 'confirm and verified'
  }
  singleEnrollee.enrollment_tracking_status[3] = {
    track_name: 'verification',
    label: 'verification',
    details: 'verification',
    date_checked: '',
    check_by: '-',
    status: 'ongoing',
    remarks: 'Please fill out evaluation form and wait for the registrars enrollment officer to verify your tracking ID, thank you!'
  }
  await $fetch(endpoint.value + "/api/admissions/submit-new-student-to-gmail-accounting-done/", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: newStudentSubmitToEmail.value,
  }).then((response) => {
    //console.log(response);
    editStatus()
    router.push("/enrollment/admin/dashboard/");
    spinner.value = false
  })
}

const evaluationDone = async () => {
  spinner.value = true
  singleEnrollee.enrollment_tracking_status[0] = {
    track_name: 'admissions',
    label: 'admission',
    details: 'Requirements',
    date_checked: '-',
    check_by: '-',
    status: 'yes',
    remarks: ''
  }
  singleEnrollee.enrollment_tracking_status[1] = {
    track_name: 'advising',
    label: 'advising',
    details: 'Course Subjects ',
    date_checked: '-',
    check_by: '-',
    status: 'yes',
    remarks: ''
  }
  singleEnrollee.enrollment_tracking_status[2] = {
    track_name: 'accounting',
    label: 'payment',
    details: 'payment',
    date_checked: '-',
    check_by: '-',
    status: 'yes',
    remarks: ''
  }
  singleEnrollee.enrollment_tracking_status[3] = {
    track_name: 'verification',
    label: 'verification',
    details: 'verification',
    date_checked: '',
    check_by: '-',
    status: 'yes',
    remarks: 'You are now officially enrolled,'
  }
  singleEnrollee.evaluation.submitted = true
  await $fetch(endpoint.value + "/api/admissions/submit-new-student-to-gmail-evaluation-done/", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: newStudentSubmitToEmail.value,
  }).then((response) => {
    //console.log(response);
    editStatus()
    router.push("/enrollment/admin/dashboard/");
    spinner.value = false
  })
}

</script>

<template>
<div class="">
  <div class="mb-5 flex min-h-screen">
    <div class="w-full">
      <div class="bg-green-800">
        <div class="lg:flex items-center mx-auto justify-between py-2">
          <div class="lg:flex items-center text-white gap-1 lg:px-1">
            <div class="lg:p-0 pt-0.5 pb-3 mx-3">
              <img class="rounded-full w-10"
                src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/logos/circleLSULogo.jpg" />
            </div>
            <!-- <div @click="toggleSideBarMenu = !toggleSideBarMenu" class="w-10 px-1.5">
                <i
                  class="fa text-3xl text-white"
                  :class="toggleSideBarMenu ? 'fa-caret-left' : 'fa-bars'"
                  aria-hidden="true"
                ></i>
              </div> -->
            <div class="flex py-2 items-center lg:border-none border-t-2 border-white justify-center">
              <i class="fa fa-user mr-2 mt-1" aria-hidden="true"></i>
              <h1 class="text-sm pt-1">
                {{ userStore.user.email }}
              </h1>
            </div>
          </div>
          <button @click="logOut" class="flex items-center hover:font-bold py-2 lg:static absolute top-2 right-2 lg:pt-2 lg:pr-2 lg:pl-4 lg:mx-4 hover:text-green-900 hover:bg-white text-white rounded-xl">
            <i class="fa fa-sign-out text-xl"></i>
            <h1 class="text-xs p-1.5">Log Out</h1>
          </button>
        </div>
      </div>
      <div class="lg:mx-10 mx-3 mt-30 flex justify-between">
        <div @click="backToMainPortalPage()"
          class="uppercase px-6 py-1 font-bold bg-blue-500 text-white w-fit my-5 rounded-2xl cursor-pointer hover:bg-white border-4 border-blue-500 hover:text-blue-500">
          Back
        </div>
      </div>
      <div class="lg:mx-10 mx-3 lg:shadow-lg">
        <div class="w-full">
          <div class="py-1 border w-full px-10 mx-auto lg:flex items-center text-center justify-center bg-green-800">
            <span class="lg:text-right lg:pr-2 text-xs text-center font-bold text-white uppercase lg:whitespace-nowrap pt-0.5">
              LSU Enrollment Tracking ID :
            </span>
            <span class="text-white font-bold">
              {{ singleEnrollee.tracking_id }}
            </span>
          </div>

          <div class="mx-auto w-fit my-20" v-if="spinner">
          <p class="text-base text-green-900 font-bold mb-4">Please wait ... </p>
          <i class="fa fa-spinner fa-spin text-8xl text-green-800 animate-spin"></i>
        </div>


          <div class="grid lg:grid-cols-4 grid-cols-2 pt-4 lg:px-10 pb-3 lg:gap-10 gap-3">
            <div class="relative w-full lg:mb-0 mb-3" 
              v-for="(j, i) in singleEnrollee.enrollment_tracking_status"
              :key="i">
              <div class="lg:items-center w-full relative">
                <p class="lg:px-3 px-4 lg:py-2 lg:mx-0 ml-0 h-fit w-full text-center font-bold" 
                :class="
                  [j.status === 'yes' ? 'bg-green-900 text-white' : ''],
                  [j.status === 'no' ? 'bg-red-700 text-white' : ''],
                  [j.status === '' ? 'bg-black text-white' : ''],
                  [j.status === 'ongoing' ? 'bg-blue-800 text-white' : '']
                ">
                  {{ i + 1 }}
                </p>

              </div>
              <div class="px-3 text-center shadow lg:py-4 py-2" 
              
              :class="i === 0 ? '' : ''">
                <p class="uppercase lg:text-sm text-xs font-bold" 
                  :class="
                    [j.status === 'yes' ? 'text-green-900' : ''],
                    [j.status === 'no' ? 'text-red-700' : ''],
                    [j.status === '' ? 'text-black' : ''],
                    [j.status === 'ongoing' ? 'text-blue-800' : '']
                  ">
                  {{ j.track_name }}
                </p>
                <span class="text-xs capitalize mb-4" 
                
                  :class="
                    [j.status === 'yes' ? 'text-green-900' : ''],
                    [j.status === 'no' ? 'text-red-700' : ''],
                    [j.status === '' ? 'text-black' : ''],
                    [j.status === 'ongoing' ? 'text-blue-800' : '']
                  "
                  
                  >
                  {{ j.details }}
                </span>



                <div 
                  v-if="(
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>'
                  )
                  && 
                  j.track_name === 'admissions'"
                  

                                  
                  >
                  <div class="text-xs capitalize mt-2 gap-3"
                 >
                  <label 
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="text-left mb-1 block font-bold capitalize pr-3">
                    status
                  </label>
                  <select
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                    class="block w-full px-2 py-1 capitalize shadow-lg rounded-sm text-xs -ml-1"
                    v-model="j.status">
                    <option value="" disabled selected hidden>status
                    </option>
                    <option value="yes">done</option>
                    <option value="ongoing">ongoing</option>
                    <option value="no">pending</option>
                  </select>
                </div>
                <div class="text-xs capitalize mt-2 gap-3"
                  >
                  <label
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  class="text-left mb-1 block font-bold capitalize pr-3">
                    date checked
                  </label>
                  <input v-model="j.date_checked"
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                    class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    :placeholder="j.date_checked" />
                </div>
                <div class="text-xs capitalize mt-2 gap-3"
                  >
                  <label
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="text-left mb-1 block font-bold capitalize pr-3">
                    check by
                  </label>
                  <input v-model="j.check_by"
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                    class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    :placeholder="j.check_by" />
                </div>

                <div class="text-xs capitalize mt-2 gap-3"
                  >
                  <label 
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="text-left mb-1 block font-bold capitalize text-green-900 pr-3">
                    Remarks
                  </label>
                  <input v-model="j.remarks"
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                    class="block w-full px-2 py-1 capitalize border shadow-lg rounded-sm text-xs -ml-1"
                    :placeholder="j.remarks" />
                </div>
                </div>

                <div 
                  v-if="(
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>'
                  )
                  && 
                  j.track_name === 'advising'"
                  
                  
                  >
                  <div class="text-xs capitalize mt-2 gap-3"
                 >
                  <label
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="text-left mb-1 block font-bold capitalize  pr-3"
                  >
                    status
                  </label>
                  <select
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                    class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    v-model="j.status">
                    <option value="" disabled selected hidden>status
                    </option>
                    <option value="yes">done</option>
                    <option value="ongoing">ongoing</option>
                    <option value="no">pending</option>
                  </select>
                </div>
                <div class="text-xs capitalize mt-2 gap-3"
                 >
                  <label 
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  
                  class="text-left mb-1 block font-bold capitalize  pr-3">
                    date checked
                  </label>
                  <input v-model="j.date_checked"
                    class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    :placeholder="j.date_checked" 
                    :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                    />
                </div>
                <div class="text-xs capitalize mt-2 gap-3"
                 >
                  <label 
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="text-left mb-1 block font-bold capitalize  pr-3">
                    check by
                  </label>
                  <input v-model="j.check_by"
                    
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    :placeholder="j.check_by" />
                </div>

                <div class="text-xs capitalize mt-2 gap-3"
                 >
                  <label 
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="text-left mb-1 block font-bold capitalize  pr-3">
                    Remarks
                  </label>
                  <input v-model="j.remarks"
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    :placeholder="j.remarks" />
                </div>
                </div>


                <div 
                  v-if="(
                  userStore.user.email === '<EMAIL>' ||             
                  userStore.user.email === '<EMAIL>'
                  )
                  && 
                  j.track_name === 'accounting'">
                  <div class="text-xs capitalize mt-2 gap-3"
                  
                  
                  
                  >
                  <label 
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="text-left mb-1 block font-bold capitalize pr-3">
                    status
                  </label>
                  <select

                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "

                    class="block w-full px-2 py-1 capitalize shadow-lg rounded-sm text-xs -ml-1"
                    v-model="j.status">
                    <option value="" disabled selected hidden>status
                    </option>
                    <option value="yes">done</option>
                    <option value="ongoing">ongoing</option>
                    <option value="no">pending</option>
                  </select>
                </div>
                <div class="text-xs capitalize mt-2 gap-3"
                  
                
                
                >
                  <label 
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="text-left mb-1 block font-bold capitalize  pr-3">
                    date checked
                  </label>
                  <input v-model="j.date_checked"
                    class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                    :placeholder="j.date_checked" />
                </div>
                <div class="text-xs capitalize mt-2 gap-3"
                  
                
                
                >
                  <label 
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="text-left mb-1 block font-bold capitalize  pr-3">
                    check by
                  </label>
                  <input v-model="j.check_by"
                    
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    :placeholder="j.check_by" />
                </div>

                <div class="text-xs capitalize mt-2 gap-3"
                  
                
                
                >
                  <label
                  
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  class="text-left mb-1 block font-bold capitalize  pr-3">
                    Remarks
                  </label>
                  <input v-model="j.remarks"
                    class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                    :placeholder="j.remarks" />
                </div>
                </div>



                <div 
                  v-if="(
                    userStore.user.email === '<EMAIL>' ||   
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>' ||
                    userStore.user.email === '<EMAIL>'
                  )
                  && 
                  j.track_name === 'verification'">
                  <div class="text-xs capitalize mt-2 gap-3"
                  >
                  <label 
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="text-left mb-1 block font-bold capitalize  pr-3">
                    status
                  </label>
                  <select
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                    class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    v-model="j.status">
                    <option value="" disabled selected hidden>status
                    </option>
                    <option value="yes">done</option>
                    <option value="ongoing">ongoing</option>
                    <option value="no">pending</option>
                  </select>
                </div>
                <div class="text-xs capitalize mt-2 gap-3"
                  >
                  <label 
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="text-left mb-1 block font-bold capitalize  pr-3">
                    date checked
                  </label>
                  <input v-model="j.date_checked"

                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                    class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    :placeholder="j.date_checked" />
                </div>
                <div class="text-xs capitalize mt-2 gap-3"
                  >
                  <label 
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  
                  class="text-left mb-1 block font-bold capitalize  pr-3">
                    check by
                  </label>
                  <input v-model="j.check_by"
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                    class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    :placeholder="j.check_by" />
                </div>

                <div class="text-xs capitalize mt-2 gap-3"
                  >
                  <label
                  
                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                  class="text-left mb-1 block font-bold capitalize  pr-3">
                    Remarks
                  </label>
                  <input v-model="j.remarks"

                  :class="
                    [j.status === 'yes' ? 'border-green-900 text-green-900 ' : ''],
                    [j.status === 'no' ? 'border-red-700 text-red-700 ' : ''],
                    [j.status === '' ? 'border-black text-black' : ''],
                    [j.status === 'ongoing' ? 'border-blue-800 text-blue-800 ' : '']
                  "
                    class="block w-full px-2 py-1 capitalize border  shadow-lg rounded-sm text-xs -ml-1"
                    :placeholder="j.remarks" />
                </div>
                </div>
              
              </div>

              <!-- {{ j }} use filter then check correspond object if has done -->
            </div>
          </div>
          <ul class="lg:flex lg:justify-center lg:mx-10 mx-3 text-center lg:mb-0 mb-5 gap-10 border-t-2 border-gray-300 pt-5">
            <li 
            v-if="
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>' ||
              userStore.user.email === '<EMAIL>'
            "
            @click="admissionDone"
              class="mb-5 cursor-pointer border-2 border-green-800 rounded-lg hover:text-green-800 hover:bg-white bg-green-800 text-white font-bold px-5 py-2">
              Admission Confirm</li>



            <li 
              v-if="
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>'

              "
            
            
            @click="advisingDone"
              class="mb-5 cursor-pointer border-2 border-green-800 rounded-lg hover:text-green-800 hover:bg-white bg-green-800 text-white font-bold px-5 py-2">
              Advising Confirm</li>


              
            <li 
              v-if="
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>'
              "
            
            
            @click="accountingDone"
              class="mb-5 cursor-pointer border-2 border-green-800 rounded-lg hover:text-green-800 hover:bg-white bg-green-800 text-white font-bold px-5 py-2">
              Accounting Confirm</li>




            <li 
              v-if="
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>' ||
                userStore.user.email === '<EMAIL>'
              "
              @click="evaluationDone"
              class="mb-5 cursor-pointer border-2 border-green-800 rounded-lg hover:text-green-800 hover:bg-white bg-green-800 text-white font-bold px-5 py-2">
              Verification Confirm</li>
          </ul>



        </div>
      </div>

      <div class="w-fit mx-auto">
          <div @click="editStatus()"
            class="uppercase px-6 py-1 my-30 font-bold bg-yellow-500 text-white w-fit lg:my-5 rounded-2xl 
            cursor-pointer hover:bg-white border-4 border-yellow-500 hover:text-yellow-500">
            Update Remarks
          </div>
        </div>


      <div class="lg:mx-10 mx-3">
        <div class="border-t-2 border-green-700 mt-5 shadow-lg">
          <div class="">
            <div class="border-b-4">

              <div @click="studentPersonalDetailsToggle = !studentPersonalDetailsToggle"
                class="flex justify-between px-3 items-center cursor-pointer"
                :class="studentPersonalDetailsToggle ? 'bg-white text-green-900 border-b-4 border-green-800' : 'bg-green-600 text-white'">
                <p class="uppercase font-bold lg:text-sm text-xs">
                  Personal Details
                </p>
                <span>
                  <i class="fa text-2xl" :class="studentPersonalDetailsToggle ? 'fa-caret-up' : 'fa-caret-down'"></i>
                </span>
              </div>


              <div class="mt-3 px-3 pb-5">
                <div>
                  <div class="lg:flex gap-3">
                    <div class="lg:block hidden">
                      <label class="text-[10px] text-gray-300 lg:block hidden">Title</label>
                      <div>
                        <input type="text" class="lg:w-20 w-full capitalize border-b-2 border-t-0 border-x-0 border-green-700 
                                                                    shadow-lg rounded-sm h-9 text-xs"
                          v-model="singleEnrollee.student_personal_info.title" placeholder="title" />
                      </div>
                    </div>
                    <div class="mb-3 w-full">
                      <div class="lg:flex w-full gap-3">

                        <div class="flex gap-3 w-full lg:mb-0 mb-3">
                          <div class="w-full">
                            <label class="text-[10px] text-gray-300 lg:block hidden">Last
                              Name</label>
                            <input type="text" class="w-full capitalize border-b-2 border-t-0 border-x-0 border-green-700 
                                                                            shadow-lg rounded-sm h-9 text-xs"
                              placeholder="Last Name" v-model="singleEnrollee.student_personal_info.lastname" />
                          </div>
                          <div class="w-full">
                            <label class="text-[10px] text-gray-300 lg:block hidden">First
                              Name</label>
                            <input type="text" class="w-full capitalize border-b-2 border-t-0 border-x-0 border-green-700 
                                                                            shadow-lg rounded-sm h-9 text-xs"
                              placeholder="First Name" v-model="singleEnrollee.student_personal_info.firstname" />
                          </div>

                        </div>
                        <div class="lg:w-8/12 w-full lg:mb-0 mb-3">
                          <label class="text-[10px] text-gray-300 lg:block hidden">Middle
                            Name</label>
                          <div class="flex gap-3">

                            <div class="flex">
                              <span class="block">
                                <span class="flex items-center mr-3 mb-1">
                                  <input type="radio" value="yes"
                                    v-model="singleEnrollee.student_personal_info.has_middlename" />
                                  <label class=" text-xs ml-2 capitalize">yes</label>
                                </span>
                                <span class="flex items-center mr-3">
                                  <input type="radio" value="no"
                                    v-model="singleEnrollee.student_personal_info.has_middlename" />
                                  <label class=" text-xs ml-2 capitalize">no</label>
                                </span>
                              </span>
                            </div>
                            <div class="w-full">
                              <input
                                :class="singleEnrollee.student_personal_info.has_middlename === 'yes' ? '' : 'hidden'"
                                type="text" class="w-full capitalize border-b-2 border-t-0 border-x-0 border-green-700 
                                                                            shadow-lg rounded-sm h-9 text-xs"
                                placeholder="Middle Name" v-model="singleEnrollee.student_personal_info.middlename" />
                            </div>
                          </div>
                        </div>
                        <div class="flex gap-3">


                          <div class="lg:hidden flex">
                            <label class="text-[10px] text-gray-300 lg:block hidden">Title</label>
                            <div>
                              <input type="text" class="lg:w-20 w-full capitalize border-b-2 border-t-0 border-x-0 border-green-700 
                                                                    shadow-lg rounded-sm h-9 text-xs"
                                v-model="singleEnrollee.student_personal_info.title" placeholder="title" />
                            </div>
                          </div>

                          <div>
                            <label class="text-[10px] text-gray-300 lg:block hidden">Suffix</label>
                            <div class="flex">
                              <input type="text" class="lg:w-20 w-full capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg 
                                                                    rounded-sm h-9 text-xs"
                                v-model="singleEnrollee.student_personal_info.extension_or_suffix_name"
                                placeholder="Suffix" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                  </div>
                </div>
                <div class="lg:flex grid grid-cols-2 gap-3">
                  <div class="">
                    <label class="text-[10px] text-gray-300 lg:block hidden">Birth
                      Sex</label>
                    <div
                      class="flex lg:w-fit lg:gap-5 pt-2.5 pb-2 lg:pl-4 pl-1 lg:pr-5 pr-1 shadow bg-white border-b-2 border-green-700">
                      <div class="flex mr-3">
                        <input type="radio" value="Female" v-model="singleEnrollee.student_personal_info.birth_sex"
                          class="lg:mr-2 mr-1" />
                        <label class="text-[10px]">
                          Female
                        </label>
                      </div>
                      <div class="flex">
                        <input type="radio" value="Male" v-model="singleEnrollee.student_personal_info.birth_sex"
                          class="lg:mr-2 mr-1" />
                        <label class="text-[10px]">
                          Male
                        </label>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label class="text-[10px] text-gray-300 lg:block hidden">Birth
                      Date</label>
                    <div>
                      <input type="date" class="lg:w-44 w-full border-b-2 border-t-0 border-x-0 
                                                                border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        placeholder="Date of Birth" v-model="singleEnrollee.student_personal_info.birth_date" />
                    </div>
                  </div>
                  <div>
                    <label class="text-[10px] text-gray-300 lg:block hidden">Age</label>
                    <div>
                      <select class="lg:w-24 w-full px-3 capitalize border-b-2 border-t-0 border-x-0
                                                                 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_personal_info.age">
                        <option value="" disabled selected hidden>Age</option>
                        <option :value="j" v-for="(j, i) in ageRange" :key="i">
                          {{ j }}
                        </option>
                      </select>
                    </div>
                  </div>
                  <div>
                    <label class="text-[10px] text-gray-300 lg:block hidden">
                      Birth Order
                    </label>
                    <div>
                      <select
                        class="lg:w-fit w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_personal_info.birth_order">
                        <option value="" disabled selected hidden>Birth Order
                        </option>
                        <option :value="j" v-for="(j, i) in birthOrderList" :key="i">{{
                          j }}
                        </option>
                      </select>
                    </div>
                  </div>
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300 lg:block hidden">Birth
                      Place</label>
                    <div>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_personal_info.birth_place" placeholder="Birth Place" />
                    </div>
                  </div>
                  <div class="mb-3 w-full">
                    <label class="text-[10px] text-gray-300 lg:block hidden">Religion</label>
                    <div>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_personal_info.religion" placeholder="Religion" />
                    </div>
                  </div>

                </div>
                <div class="lg:flex lg:gap-10">
                  <div class="lg:flex gap-3 grid grid-cols-2">
                    <div>
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Citizenship
                      </label>
                      <div>
                        <select
                          class="lg:w-32 w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                          v-model="singleEnrollee.student_personal_info.citizenship">
                          <option value="" disabled selected hidden>
                            Citizenship
                          </option>
                          <option :value="j" v-for="(j, i) in citizenshipList" :key="i">{{ j }}
                          </option>
                        </select>
                      </div>
                    </div>
                    <div>
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Civil Status
                      </label>
                      <div>
                        <select
                          class="lg:w-44 w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                          v-model="singleEnrollee.student_personal_info.civil_status">
                          <option value="" disabled selected hidden>Civil
                            Status
                          </option>
                          <option :value="j" v-for="(j, i) in civilStatusList" :key="i">{{ j }}
                          </option>
                        </select>
                      </div>
                    </div>
                    <div>
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Languages Spoken
                      </label>
                      <div>
                        <select
                          class="lg:w-44 w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                          v-model="singleEnrollee.student_personal_info.nationality">
                          <option value="" disabled selected hidden>
                            Languages Spoken
                          </option>
                          <option :value="j" v-for="(j, i) in languagesSpokenList" :key="i">
                            {{ j }}
                          </option>
                        </select>
                      </div>
                    </div>
                    <div>
                      <label class="text-[10px] text-gray-300 lg:block hidden">Ethnicity</label>
                      <div>
                        <input type="text"
                          class="lg:w-44 w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                          v-model="singleEnrollee.student_personal_info.ethnicity" placeholder="Ethnicity" />
                      </div>
                    </div>

                  </div>
                  <div class="lg:flex items-center pt-3">
                    <p class="text-xs lg:mb-0 mb-3 lg:mr-5 text-green-900">
                      Do you
                      belong to a
                      Tribal/Indigenous Community?</p>
                    <div class="flex items-center">
                      <span class="flex items-center">
                        <input type="radio" value="yes"
                          v-model="singleEnrollee.student_tribal_or_indigenous_community.option" />
                        <label class="text-xs px-1">
                          Yes
                        </label>
                        <span class="flex items-center whitespace-nowrap"
                          :class="singleEnrollee.student_tribal_or_indigenous_community.option === 'yes' ? '' : 'hidden'">
                          <input type="text" v-model="singleEnrollee.student_tribal_or_indigenous_community.name"
                            placeholder="Name of Tribe"
                            class="w-fit px-3 ml-2 mr-1 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs" />
                        </span>
                      </span>
                      <span class="flex items-center mx-1">
                        <input type="radio" value="no"
                          v-model="singleEnrollee.student_tribal_or_indigenous_community.option" />
                        <label class="text-xs px-1">
                          No
                        </label>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="border-b-4">


              <div @click="studentContactInformationToggle = !studentContactInformationToggle"
                class="flex justify-between px-3 items-center cursor-pointer"
                :class="studentContactInformationToggle ? 'bg-white text-green-900 border-b-4 border-green-800' : 'bg-green-600 text-white'">
                <p class="uppercase font-bold lg:text-sm text-xs">
                  Contact Information
                </p>
                <span>
                  <i class="fa text-2xl" :class="studentContactInformationToggle ? 'fa-caret-up' : 'fa-caret-down'"></i>
                </span>
              </div>


              <div class="mt-3 px-3 lg:pb-5 pb-3">
                <div class="gap-3">
                  <div class="lg:flex gap-3">
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Street or Purok
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.permanent_living_home_address_category.street_or_purok"
                        placeholder="Street or Purok" />
                    </div>
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Barangay or Village
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.permanent_living_home_address_category.barangay_or_village"
                        placeholder="Barangay or Village" />
                    </div>
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        City or Municipality
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.permanent_living_home_address_category.city_or_municipality"
                        placeholder="City or Municipality" />
                    </div>
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Zip Code
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.permanent_living_home_address_category.zipcode"
                        placeholder="Zip Code" />
                    </div>
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Province or State
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.permanent_living_home_address_category.province_or_state"
                        placeholder="Province or State" />
                    </div>
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Region
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.permanent_living_home_address_category.region"
                        placeholder="Region" />
                    </div>

                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Country
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.permanent_living_home_address_category.country"
                        placeholder="Country" />
                    </div>
                  </div>
                </div>
                <div class="mb-3 lg:flex items-center lg:pt-3">
                  <p class="text-xs text-green-900 mr-3 lg:pt-1 lg:text-left text-center">
                    {{ singleEnrollee.student_contact_info.the_same_address.question }}
                  </p>


                  <div class="flex gap-3 pt-1 lg:mx-0 mx-auto w-fit lg:mb-0 mb-4 lg:mt-0 mt-2">
                    <div @click="theSameAddressBtn('yesStudentContactInfo')"
                      class="text-xs capitalize cursor-pointer px-3 rounded-lg hover:text-green-800 hover:bg-white border-2 border-green-800"
                      :class="displaySecondaryContactAddress === 'yes' ? 'text-white bg-green-800' : 'text-green-800 bg-white border-2'">
                      yes</div>


                    <div @click="theSameAddressBtn('noStudentContactInfo')"
                      class="text-xs capitalize cursor-pointer  px-3 rounded-lg hover:text-red-800 hover:bg-white border-2 border-red-800"
                      :class="displaySecondaryContactAddress === 'no' ? 'text-white bg-red-800' : 'text-red-800 bg-white border-2'">
                      no</div>
                  </div>

                </div>
                <div class="gap-3 mb-3"
                  :class="displaySecondaryContactAddress === 'yes' ? 'hidden':''">
                  <div class="lg:flex gap-3">
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Street or Purok
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.current_or_present_living_home_address_category.street_or_purok"
                        placeholder="Street or Purok" />
                    </div>
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Barangay or Village
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.current_or_present_living_home_address_category.barangay_or_village"
                        placeholder="Barangay or Village" />
                    </div>
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        City or Municipality
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.current_or_present_living_home_address_category.city_or_municipality"
                        placeholder="City or Municipality" />
                    </div>
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Zip Code
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.current_or_present_living_home_address_category.zipcode"
                        placeholder="Zip Code" />
                    </div>
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Province or State
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.current_or_present_living_home_address_category.province_or_state"
                        placeholder="Province or State" />
                    </div>
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Region
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.current_or_present_living_home_address_category.region"
                        placeholder="Region" />
                    </div>
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Country
                      </label>
                      <input type="text"
                        class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                        v-model="singleEnrollee.student_contact_info.current_or_present_living_home_address_category.country"
                        placeholder="Country" />
                    </div>
                  </div>
                </div>
                <div class="lg:flex gap-3">
                  <div class="flex w-full gap-3">
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Primary Contact Number</label>
                      <div>
                        <input type="text"
                          class="w-full text-xs px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9"
                          placeholder="(+63) XXX-XXX-XXXX"
                          v-model="singleEnrollee.student_contact_info.contact.primary_number" />
                      </div>
                    </div>
                    <div class="w-full lg:mb-0 mb-3">
                      <label class="text-[10px] text-gray-300 lg:block hidden">
                        Alternate Contact Number</label>
                      <div>
                        <input type="text"
                          class="w-full text-xs px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9"
                          placeholder="(+63) XXX-XXX-XXXX"
                          v-model="singleEnrollee.student_contact_info.contact.alternate_number" />
                      </div>
                    </div>
                  </div>
                  <div class="w-full lg:mb-0 mb-3">
                    <label class="text-[10px] text-gray-300 lg:block hidden">
                      Personal Email Address
                    </label>
                    <div>
                      <input type="email"
                        class="w-full text-xs px-3 border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9"
                        placeholder="<EMAIL>"
                        v-model="singleEnrollee.student_contact_info.contact.personal_email_address" />
                    </div>
                  </div>
                  <div class="w-full lg:mb-0 mb-3">
                    <label class="text-[10px] text-gray-300 lg:block hidden">
                      LSU Email</label>
                    <div>

                      <input type="email"
                        class="w-full text-xs px-3 border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9"
                        placeholder="<EMAIL>"
                        v-model="singleEnrollee.student_contact_info.contact.lsu_email_address" />
                    </div>
                  </div>
                  <div class="w-full lg:mb-0 mb-3">
                    <label class="text-[10px] text-gray-300 lg:block hidden">
                      LSU Student ID Number</label>
                    <div>

                      <input type="text"
                        class="w-full text-xs px-3 border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9"
                        placeholder="e.g LSU221123265" v-model="singleEnrollee.student_lsu_id_number" />
                    </div>
                  </div>
                </div>
                <div class="lg:flex gap-3 mt-5 border-t-4 pb-3 border-gray pt-6 lg:px-10">
                  <div class="w-full lg:mb-0 mb-3">
                    <label class="text-sm mb-1 text-black lg:block hidden">
                      College
                    </label>
                    <select v-model="singleEnrollee.admissions_list_filter.college"
                      class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      required>
                      <option value="" disabled>Select College or School
                      </option>
                      <option :value="c.name" v-for="(c, i) in collegeOrSchoolData" :key="i">
                        {{ c.name }}
                      </option>
                    </select>
                  </div>
                  <div class="w-full lg:mb-0 mb-3">
                    <label class="text-sm mb-1 text-black lg:block hidden">
                      Program
                    </label>
                    <select v-model="singleEnrollee.admissions_list_filter.program"
                      class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm text-black h-9 text-xs"
                      required>
                      <option value="" disabled>Select Course Program</option>
                      <option :value="cp" v-for="(cp, i) in courseOrProgramData" :key="i">
                        {{ cp }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="border-b-4" v-if="singleEnrollee.student_personal_info.citizenship === 'non-filipino'">



              <div @click="studentAlienStatusInformationToggle = !studentAlienStatusInformationToggle"
                class="flex justify-between px-3 items-center cursor-pointer"
                :class="studentAlienStatusInformationToggle ? 'bg-white text-green-900 border-b-4 border-green-800' : 'bg-green-600 text-white'">
                <p class="uppercase font-bold lg:text-sm text-xs">
                  Alien status information
                </p>
                <span>
                  <i class="fa text-2xl"
                    :class="studentAlienStatusInformationToggle ? 'fa-caret-up' : 'fa-caret-down'"></i>
                </span>
              </div>



              <div class="mt-3 px-3 pb-5" v-if="studentAlienStatusInformationToggle">
                <div class="lg:flex grid grid-cols-2 gap-3 mb-2">
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      Citizenship</label>
                    <input type="text"
                      class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.citizenship" placeholder="Citizenship" />
                  </div>
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      Visa Status
                    </label>
                    <input type="text"
                      class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.visa_status" placeholder="Visa Status" />
                  </div>
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      Last Day Authorized Stay
                    </label>
                    <input type="text"
                      class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.last_day_of_authorized_stay"
                      placeholder="Last Day of Authorized Stay" />
                  </div>
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      Agent Name
                    </label>
                    <input type="text"
                      class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.agent_name" placeholder="Agent Name" />
                  </div>
                </div>
                <div class="lg:flex grid grid-cols-2 gap-3 mb-2">
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      Passport Number
                    </label>
                    <input type="text"
                      class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.passport_number" placeholder="Passport Number" />
                  </div>
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      Passport: Place Issued
                    </label>
                    <input type="text"
                      class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.passport_place_issued"
                      placeholder="Passport: Place Issued" />
                  </div>
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      Passport: Date Issued
                    </label>
                    <input type="date"
                      class="w-full px-3 uppercase border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.passport_date_issued"
                      placeholder="Passport: Date Issued" />
                  </div>
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      Passport: Date of Expiry
                    </label>
                    <input type="date"
                      class="w-full px-3 uppercase border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.passport_date_of_expiry"
                      placeholder="Passport: Date of Expiry" />
                  </div>
                </div>
                <div class="lg:flex grid grid-cols-2 gap-2">
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      ACR: I-CARD: Date Issued
                    </label>
                    <input type="date"
                      class="w-full px-3 uppercase border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.acricard_date_issued"
                      placeholder="ACRICARD Date Issued" />
                  </div>
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      ACR: I-CARD: Expiry Date
                    </label>
                    <input type="date"
                      class="w-full px-3 uppercase border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.acricard_date_of_expiry"
                      placeholder="ACRICARD Date of Expiry" />
                  </div>
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      CRTS: Date Issued
                    </label>
                    <input type="date"
                      class="w-full px-3 uppercase border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.crts_date_issued"
                      placeholder="CRTS Date Issued" />
                  </div>
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      CRTS: Date of Expiry
                    </label>
                    <input type="date"
                      class="w-full px-3 uppercase border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.crts_date_of_expiry"
                      placeholder="CRTS Date of Expiry" />
                  </div>
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      SSP: Date Issued
                    </label>
                    <input type="date"
                      class="w-full px-3 uppercase border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.ssp_date_issued" placeholder="SSP Date Issued" />
                  </div>
                  <div class="w-full">
                    <label class="text-[10px] text-gray-300">
                      SSP: Date of Expiry
                    </label>
                    <input type="date"
                      class="w-full px-3 uppercase border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs"
                      v-model="singleEnrollee.alien_status_information.ssp_date_of_expiry"
                      placeholder="SSP Date of Expiry" />
                  </div>
                </div>
              </div>

            </div>
            <div class="border-b-4">
              <div class="lg:flex justify-between bg-green-600 cursor-pointer
              text-white lg:px-5 pt-3 pb-3">
                  <div
                      class="flex justify-between marker:items-center cursor-pointer lg:px-0 px-3">
                      <p class="font-bold uppercase">
                          returnee student
                      </p>
                  </div>
                  <div class="flex items-center px-3">
                      <label class="pr-5">Are you a Returnee?</label>
                      <div class="flex">
                          <div class="flex items-center mr-6">
                              <input type="radio" value="yes"
                                  v-model="singleEnrollee.returnee_student.is_student_returnee"
                                  class="mr-1" />
                              <label class="text-sm">
                                  Yes
                              </label>
                          </div>
                          <div class="flex items-center">
                              <input type="radio" value="no"
                                  v-model="singleEnrollee.returnee_student.is_student_returnee"
                                  class="mr-1" />
                              <label class="text-sm">
                                  No
                              </label>
                          </div>
                      </div>
                  </div>
              </div>
              
              <div v-if="singleEnrollee.returnee_student.is_student_returnee === 'yes'">
                  <div class="lg:px-10 px-3 lg:flex gap-3 mt-5">

                      <div class="lg:w-6/12 w-full lg:mb-0 mb-5">
                          <label
                              class="font-bold lg:text-sm text-xs text-green-900 w-fit whitespace-nowrap pr-10 pb-1 block">
                              Last Term
                          </label>
                          <select
                              v-model="singleEnrollee.admissions_list_filter.last_term_enrolled"
                              class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs">
                              <option class="text-xs text-green-900 py-2"
                                  value="Select Term" disabled>
                                  Select Term
                              </option>
                              <option class="text-xs text-green-900 py-2"
                                  value="1st Semester">1st
                                  Semester</option>
                              <option class="text-xs text-green-900 py-2"
                                  value="2nd Semester">2nd
                                  Semester</option>
                              <option class="text-xs text-green-900 py-2" value="Summer">
                                  Summer
                              </option>
                          </select>
                      </div>
                      <div class="lg:w-6/12 w-full lg:mb-0 mb-5">
                          <label
                              class="font-bold lg:text-sm text-xs text-green-900 w-fit whitespace-nowrap pr-10 pb-1 block">
                              Last Academic Year
                          </label>
                          <select
                              v-model="singleEnrollee.admissions_list_filter.last_academic_year_enrolled"
                              class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-9 text-xs">
                              <option class="text-xs text-green-900 py-2"
                                  value="Select Academic Year" disabled>Select
                                  Academic Year
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2010 - 2011">
                                  A.Y.
                                  2010 - 2011
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2011 - 2012">
                                  A.Y.
                                  2011 - 2012
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2012 - 2013">
                                  A.Y.
                                  2012 - 2013
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2013 - 2014">
                                  A.Y.
                                  2013 - 2014
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2014 - 2015">
                                  A.Y.
                                  2014 - 2015
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2015 - 2016">
                                  A.Y.
                                  2015 - 2016
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2016 - 2017">
                                  A.Y.
                                  2016 - 2017
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2017 - 2018">
                                  A.Y.
                                  2017 - 2018
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2018 - 2019">
                                  A.Y.
                                  2018 - 2019
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2019 - 2020">
                                  A.Y.
                                  2019 - 2020
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2020 - 2021">
                                  A.Y.
                                  2020 - 2021
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2021 - 2022">
                                  A.Y.
                                  2021 - 2022
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2022 - 2023">
                                  A.Y.
                                  2022 - 2023
                              </option>
                              <option class="text-xs text-green-900 py-2" value="A.Y.
2023 - 2024">
                                  A.Y.
                                  2023 - 2024
                              </option>
                          </select>
                      </div>
                      <div class="w-full lg:mb-0 mb-5">
                          <div class="block">
                              <p
                                  class=" font-bold lg:text-sm text-xs text-green-900 w-fit lg:whitespace-nowrap pr-10 pb-1">
                                  {{ singleEnrollee.returnee_student.question }}</p>


                              <div class="block bg-white w-full pt-2 pb-1.5 px-5


capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-auto text-xs

">
                                  <ul class="lg:flex justify-between gap-10 bg-white">
                                      <li v-for="(li, i) in singleEnrollee.returnee_student.reason_dropping_out_college"
                                          :key="i" class="lg:mb-0 mb-2 lg:whitespace-nowrap">
                                          <input v-model="li.value" :value="li.label"
                                              type="checkbox" class="mr-2" />
                                          {{ li.label }}
                                          <input v-model="li.description"
                                              placeholder="please specify"
                                              :class="li.label === 'Others' && li.value ? '' : 'hidden'"
                                              class="ml-3 lg:w-44 w-10/12 px-3 capitalize border-b-2 py-1 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-[23px] text-xs" />
                                      </li>
                                  </ul>
                              </div>
                          </div>
                      </div>

                  </div>












                  <div class="w-full items-center my-4 lg:px-10 px-3">
                      <p
                          class="font-bold lg:text-sm text-xs text-green-900 w-fit lg:whitespace-nowrap pr-5 pb-2">
                          Reason(s) for returning to LSU.
                      </p>
                      <textarea
                          class="w-full px-3 capitalize border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-auto text-xs py-2"
                          rows="1"
                          v-model="singleEnrollee.returnee_student.reasons_for_returning_lsu"
                          placeholder="answer here ...">
</textarea>
                  </div>
              </div>
            </div>               
          </div>
        </div>
        <div class="mt-30 w-fit mx-auto" 
        
        
        v-if="
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>' ||
          userStore.user.email === '<EMAIL>'
        ">
          <div @click="editStatus()"
            class="uppercase px-6 py-1 font-bold bg-yellow-500 text-white w-fit my-5 rounded-2xl cursor-pointer hover:bg-white border-4 border-yellow-500 hover:text-yellow-500">
            Update
          </div>
        </div>
        <div class="lg:flex border-t-2 border-green-700 mt-5 shadow-lg">
          <div class="w-full shadow lg:px-10 px-3 py-5 text-green-900">
            <h2 class="font-bold capitalize text-green-900">HEALTH INFORMATION</h2>
            <p class="text-xs mb-3">
              <span class="font-bold text-green-900 mr-1">
                {{ staticNewStudentTexts.has_health_condition.text[0] }}
              </span>
              <span>
                {{ staticNewStudentTexts.has_health_condition.text[1] }}
              </span>
            </p>
            <p class="text-xs mb-3">
              {{ staticNewStudentTexts.has_health_condition.text[2] }}
            </p>
            <p class="text-xs mb-3">
              {{ staticNewStudentTexts.has_health_condition.text[3] }}
            </p>
            <p class="text-xs mb-3">
              {{ staticNewStudentTexts.has_health_condition.text[4] }}
            </p>
            <div class="my-3 flex items-center">
              <input type="checkbox" class="mr-1.5" v-model="singleEnrollee.has_health_condition.answer" value="true" />
              <label class="text-xs font-bold">
                {{ staticNewStudentTexts.has_health_condition.text[5] }}
              </label>
            </div>
          </div>

          <div class="w-full shadow lg:px-10 px-3 py-5 text-green-900">
            <h2 class="font-bold capitalize text-green-900">
              MEDIA RELEASE CONSENT
            </h2>
            <p class="text-xs">
              {{ staticNewStudentTexts.media_release_consent.text[0] }}
              <span class="font-bold text-green-900">
                {{ staticNewStudentTexts.media_release_consent.text[1] }}
              </span>
            </p>
            <p class="text-xs mb-3">
              {{ staticNewStudentTexts.media_release_consent.text[2] }}
            </p>
            <p class="text-xs mb-3">
              {{ staticNewStudentTexts.media_release_consent.text[3] }}
            </p>
            <ul class="list-disc lg:ml-5 ml-3 mb-3">
              <li v-for="(j, i) in staticNewStudentTexts.media_release_consent.list" :key="i" class="text-xs">
                {{ j }}
              </li>
            </ul>
            <p class="text-xs mb-3">
              {{ staticNewStudentTexts.media_release_consent.text[4] }}
            </p>
            <p class="text-xs mb-3">
              {{ staticNewStudentTexts.media_release_consent.text[5] }}
            </p>
            <p class="text-xs mb-3">
              {{ staticNewStudentTexts.media_release_consent.text[6] }}
            </p>
            <p class="text-xs mb-3">
              {{ staticNewStudentTexts.media_release_consent.text[7] }}
            </p>
            <p class="text-xs mb-3">
              {{ staticNewStudentTexts.media_release_consent.text[8] }}
            </p>
            <p class="text-xs mb-3">
              {{ staticNewStudentTexts.media_release_consent.text[9] }}
            </p>
            <p class="text-xs mb-4 text-green-800">
              {{ staticNewStudentTexts.media_release_consent.text[10] }}
            </p>
            <div class="">
              <div class="my-3 flex items-center">
                <input type="radio" class="mr-1.5" v-model="singleEnrollee.media_release_consent.answer"
                  value="AGREE" />
                <label class="text-xs font-bold text-green-900">
                  AGREE
                </label>
              </div>
              <div class="flex items-center">
                <input type="radio" class="mr-1.5" v-model="singleEnrollee.media_release_consent.answer"
                  value="DO NOT AGREE" />
                <label class="text-xs font-bold text-green-900">
                  DO NOT AGREE
                </label>
              </div>
            </div>
          </div>
          <div class="w-full shadow lg:px-10 px-3 py-5 text-green-900">
            <h2 class="font-bold capitalize text-green-900">CERTIFICATION</h2>
            <p class="text-xs font-bold text-green-900">
              {{ staticNewStudentTexts.hereby_certification.text[0] }}
            </p>
            <p class="text-xs mb-3">
              {{ staticNewStudentTexts.hereby_certification.text[1] }}
            </p>
            <div class="my-3 flex items-center">
              <input type="checkbox" class="mr-2" v-model="singleEnrollee.hereby_certification.answer" value="" />
              <label class="text-xs font-bold">
                {{ staticNewStudentTexts.hereby_certification.text[2] }}
              </label>
            </div>
          </div>
        </div>
        <div class="my-10 lg:flex">
          <p class="text-sm font-bold text-green-800 lg:mb-0 mb-2 text-center">Receipt:</p>
          <img :src="singleEnrollee.receipt.image_url" class="lg:w-5/12 mx-auto" />
        </div>
        <div v-if="userStore.user.email === '<EMAIL>'" class="w-11/12 mx-auto mb-10 border">
          <input v-model="singleEnrollee.receipt.image_url" class="w-full"/>
        </div>
        <div v-if="userStore.user.email === '<EMAIL>'" class="w-11/12 mx-auto mb-10 border">
          receipt submitted: {{ singleEnrollee.receipt.submitted }}
        </div>
        <div v-if="userStore.user.email === '<EMAIL>'" class="w-11/12 mx-auto mb-10 border">
          receipt confirm: {{ singleEnrollee.receipt.confirm }}
        </div>
        <div v-if="userStore.user.email === '<EMAIL>'" class="w-11/12 mx-auto mb-10 border">
          evaluation submitted: {{ singleEnrollee.evaluation.submitted }}
        </div>
        <div class="flex justify-between px-3 items-center cursor-pointer" @click="filterEvaluation()"
          :class="evaluationTab ? 'bg-white border-4 text-green-900 border-b-4 border-green-800' : 'bg-green-800 py-3 text-white'">
          <p class="uppercase font-bold lg:text-sm text-xs">
            Evaluation
          </p>
          <span>
            <i class="fa text-2xl" :class="evaluationTab ? 'fa-caret-up' : 'fa-caret-down'"></i>
          </span>
        </div>
        <div class="" v-if="evaluationTab">
          <div>
            <div class="border mb-10" v-for="(j, i) in evaluationWithData.main_question" :key="i">
              <div class="w-full">
                <p class="font-bold text-white bg-green-900 py-1 px-2">

                  {{ j.question_heading }}
                </p>
              </div>
              <div class="w-full">
                <div class="lg:flex gap-20 border-b-2 border-green-900 py-4">
                  <p class="lg:w-7/12 flex items-center font-bold text-sm px-3 lg:mb-0 mb-2">
                    {{ evaluationStatic.heading.question }}
                  </p>
                  <ul class="w-full flex items-center">
                    <li class="justify-center text-center w-full text-sm lg:pt-0 pt-2 lg:px-3 px-1"
                      v-for="(j, i) in evaluationStatic.heading.score" :key="i">
                      <span
                        class="block text-white py-1 px-2.5 mb-2 font-bold w-fit mx-auto rounded-full bg-green-900 ">
                        {{ j.number }}</span>
                      <span
                        class="justify-center w-full mx-auto lg:text-sm text-[8px] leading-tight font-semibold text-green-900 flex lg:h-10 h-6">
                        {{ j.text }}</span>
                    </li>
                  </ul>
                </div>
                <div class="shadow">
                  <ul>
                    <li v-for="(j, i) in j.question_list" :key="i" class="lg:flex items-center gap-20 border-b py-3">
                      <p class="lg:w-7/12 flex items-center text-sm px-3 lg:border-none border-b-2 lg:pb-0 pb-3">
                        <span class="font-bold text-white bg-green-900 px-2 text-xs rounded-full py-1 mr-3 w-fit h-fit">
                          {{ i
                            + 1 }}</span>
                        <span class="lg:text-sm text-xs">{{ j.question }}</span>
                      </p>
                      <ul class="flex items-center w-full lg:pt-0 pt-2">
                        <li v-for="(m, l) in j.score" :key="l" class="text-sm justify-center text-center w-full">
                          <span class="block lg:hidden text-xs text-green-900 font-bold">{{ m.number }}</span>
                          <span class="block">
                            <input type="radio" v-model="j.answer" :value="m.text" />
                          </span>
                        </li>
                      </ul>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="shadow border my-10 py-5 lg:px-3 px-2">
              <ul class="lg:py-3">
                <li v-for="(j, i) in evaluationWithData.sub_question" :key="i" :class="i === 0 ? 'lg:mb-7 mb-5' : ''">
                  <label
                    class="text-green-900 lg:leading-normal leading-tight font-bold mb-2 block lg:w-8/12 lg:text-sm text-xs">{{
                      j.question }}</label>

                  <textarea
                    class="py-1 w-full lg:h-auto h-40 px-2 border-b-2 border-t border-t-gray-100 border-x-0 border-green-700 shadow-lg rounded-sm text-xs"
                    id="answer" name="answer" rows="1" v-model="j.answer" placeholder="answer here ...">
            </textarea>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <DashboardFooter />
</div>
</template>
<style scoped>
/* width */
::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #ececec;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #135000;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #00c566;
}
</style>