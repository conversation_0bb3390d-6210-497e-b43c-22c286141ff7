<script setup>
import { ref, onMounted } from 'vue';
import { google } from 'googleapis';

const file = ref(null);
const uploadResult = ref(null);
const uploadError = ref(null);
const uploading = ref(false);

// Replace with your Client ID from Google Cloud Console
const CLIENT_ID = '960459011621-162ie2rg0tjnjdiv84o2shitgjo3v7eq.apps.googleusercontent.com';
const SCOPES = ['https://www.googleapis.com/auth/drive.file'];

const gapiLoaded = ref(false);
const accessToken = ref(null);

onMounted(() => {
  loadGapi();
});

const loadGapi = () => {
  gapi.load('client:auth2', initClient);
};

const initClient = () => {
  gapi.client.init({
    clientId: CLIENT_ID,
    scope: SCOPES.join(' '),
  }).then(() => {
    gapi.auth2.getAuthInstance().isSignedIn.listen(updateSigninStatus);
    updateSigninStatus(gapi.auth2.getAuthInstance().isSignedIn.get());
    gapiLoaded.value = true;
  }).catch((error) => {
    console.error('Error initializing Google API client:', error);
    uploadError.value = 'Failed to initialize Google API client';
  });
};

const updateSigninStatus = (isSignedIn) => {
  if (isSignedIn) {
    accessToken.value = gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token;
  } else {
    accessToken.value = null;
  }
};

const handleAuthClick = () => {
  if (!gapi.auth2.getAuthInstance().isSignedIn.get()) {
    gapi.auth2.getAuthInstance().signIn();
  }
};

const onFileChange = (event) => {
  file.value = event.target.files[0];
};

const uploadFile = async () => {
  if (!file.value || !accessToken.value) {
    if (!accessToken.value) {
      alert('Please sign in to Google first.');
    }
    return;
  }

  uploading.value = true;
  uploadResult.value = null;
  uploadError.value = null;

  const boundary = '-------314159265358979323846';
  const delimiter = "\r\n--" + boundary + "\r\n";
  const finalData = "\r\n--" + boundary + "--\r\n";

  const metadata = {
    'name': file.value.name,
    'mimeType': file.value.type
  };

  const multipartRequestBody =
    delimiter +
    'Content-Type: application/json\r\n\r\n' +
    JSON.stringify(metadata) +
    delimiter +
    'Content-Type: ' + file.value.type + '\r\n' +
    'Content-Transfer-Encoding: binary\r\n\r\n' +
    await readFileAsync(file.value) +
    finalData;

  try {
    const response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken.value}`,
        'Content-Type': `multipart/related; boundary="${boundary}"`
      },
      body: multipartRequestBody,
    });

    const data = await response.json();
    uploading.value = false;

    if (response.ok) {
      uploadResult.value = data;
      // Optionally send metadata to your Django backend
      await sendMetadataToDjango(data.id, data.webViewLink, file.value.name);
    } else {
      uploadError.value = data.error.message || 'Failed to upload to Google Drive';
    }
  } catch (err) {
    uploading.value = false;
    uploadError.value = err.message || 'Network error';
  }
};

const readFileAsync = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result);
    };
    reader.onerror = reject;
    reader.readAsBinaryString(file);
  });
};

const sendMetadataToDjango = async (googleDriveId, googleDriveLink, originalName) => {
  try {
    await useFetch('/api/save-metadata/', { // Adjust the API endpoint
      method: 'POST',
      body: {
        google_drive_id: googleDriveId,
        google_drive_link: googleDriveLink,
        original_name: originalName,
      },
    });
    console.log('Metadata sent to Django');
  } catch (error) {
    console.error('Error sending metadata to Django:', error);
  }
};
</script>

<template>
  <div>
    <button v-if="!accessToken && gapiLoaded" @click="handleAuthClick">
      Sign in to Google
    </button>
    <div v-if="accessToken">
      <input type="file" @change="onFileChange">
      <button @click="uploadFile" :disabled="!file || uploading">
        {{ uploading ? 'Uploading...' : 'Upload to Google Drive' }}
      </button>
    </div>

    <div v-if="uploadResult">
      <h3>Upload Successful!</h3>
      <p>Google Drive File ID: {{ uploadResult.id }}</p>
      <p>Google Drive Link: <a :href="uploadResult.webViewLink" target="_blank">Open in Google Drive</a></p>
    </div>
    <div v-if="uploadError" class="error">
      Error: {{ uploadError }}
    </div>
  </div>
</template>

<style scoped>
.error {
  color: red;
  margin-top: 10px;
}
</style>