<script setup>
import { useUserStore } from "@/stores/user";
const router = useRouter();
import _ from "lodash";
const userStore = useUserStore();
const endpoint = ref(userStore.mainDevServer);

const careers = await $fetch(endpoint.value + "/api/humanResource/list").catch(
  (error) => error.data
) || 0;

// const careers = [{
//   image_url: "https://lsu-media-styles.sgp1.digitaloceanspaces.com/storage-bucket-name/folder-location/files/humanResource/files/s1.jpg"
// }]
// console.log(careers)
</script>
<template>
  <div>
    <Header />
    <div class="">
      <div class="">
        <div class="relative">
          <img
            src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/about.jpg"
            class="align-top w-full h-auto lg:object-fill lg:block hidden"
          />
          <img
            src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/enrollment/enrollmentbg-mobile-lower-size.png"
            class="align-top w-full min-h-40 pt-12 lg:hidden block"
          />
          <div class="pt-10 absolute top-1/2 transform -translate-y-1/2 w-full">
            <h1
              class="lg:block hidden font-bold uppercase text-white lg:text-2xl text-lg w-11/12 mx-auto"
            >
              human resource
            </h1>
          </div>
          <div class="shadow-lg">
            <div class="w-11/12 mx-auto flex justify-between">
              <ul
                class="flex lasalle-green-text capitalize text-xs pt-2.5 pb-3"
              >
                <li>
                  <a href="/" class="mr-1"> Home </a>
                </li>
                <li>
                  <i class="fas fa-caret-right mr-1"></i>
                  <a href="/hr" class="mr-1"> human resource </a>
                </li>
              </ul>
              <ul class="flex hover:text-green-800 text-white">
                <li class="px-3 h-full flex items-center capitalize text-xs">
                  <a href="/hr/login" class="flex items-center">
                    <span class="">Admin Login</span>
                  </a>
                </li>
                <li
                  class="bg-green-800 px-3 h-full flex items-center capitalize text-xs"
                >
                  <a href="/hr/login" class="flex items-center">
                    <i class="fa fa-user text-white" aria-hidden="true"></i>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div
          class="w-11/12 mx-auto lg:shadow lg:px-10 lg:py-2 lg:my-10 rounded-lg"
        >
          <h1
            class="text-center text-green-700 font-bold lg:text-3xl text-lg capitalize mt-4 lg:mb-0 mb-3"
          >
            career opportunities
          </h1>
          <ul class="lg:grid grid-cols-3 gap-5 lg:py-10">
            <li
              v-for="(c, i) in _.orderBy(careers, 'created_at', 'desc')"
              :key="i"
              class=""
            >
              <div v-for="(j, k) in c.image_link" :key="k" class="mb-5">
                <img
                  class="mx-auto w-full border shadow-lg"
                  :src="`${j.url}`"
                />
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>
<style scope></style>
