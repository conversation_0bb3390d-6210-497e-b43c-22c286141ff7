.md-editor .md-editor-preview {
    --md-theme-color: var(--md-color);
    --md-theme-color-reverse: #eee;
    --md-theme-color-hover: #eee;
    --md-theme-color-hover-inset: #ddd;
    --md-theme-link-color: #2d8cf0;
    --md-theme-link-hover-color: #73d13d;
    --md-theme-border-color: #e6e6e6;
    --md-theme-border-color-reverse: #bebebe;
    --md-theme-border-color-inset: #d6d6d6;
    --md-theme-bg-color: #fff;
    --md-theme-bg-color-inset: #ececec;
    --md-theme-bg-color-scrollbar-track: #e2e2e2;
    --md-theme-bg-color-scrollbar-thumb: rgba(0, 0, 0, .3019607843);
    --md-theme-bg-color-scrollbar-thumb-hover: rgba(0, 0, 0, .3490196078);
    --md-theme-bg-color-scrollbar-thumb-active: rgba(0, 0, 0, .3803921569);
    --md-theme-code-copy-tips-color: inherit;
    --md-theme-code-copy-tips-bg-color: #fff;
    --md-theme-code-active-color: #61aeee
}

.md-editor-dark .md-editor-preview {
    --md-theme-color: var(--md-color);
    --md-theme-color-reverse: #222;
    --md-theme-color-hover: #191919;
    --md-theme-color-hover-inset: #444;
    --md-theme-link-color: #2d8cf0;
    --md-theme-link-hover-color: #73d13d;
    --md-theme-border-color: #2d2d2d;
    --md-theme-border-color-reverse: #e6e6e6;
    --md-theme-border-color-inset: #5a5a5a;
    --md-theme-bg-color: #000;
    --md-theme-bg-color-inset: #111;
    --md-theme-bg-color-scrollbar-track: #0f0f0f;
    --md-theme-bg-color-scrollbar-thumb: #2d2d2d;
    --md-theme-bg-color-scrollbar-thumb-hover: #3a3a3a;
    --md-theme-bg-color-scrollbar-thumb-active: #3a3a3a;
    --md-theme-code-copy-tips-color: inherit;
    --md-theme-code-copy-tips-bg-color: #3a3a3a;
    --md-theme-code-active-color: #e6c07b
}

.md-editor-scrn span[rn-wrapper] {
    position: absolute;
    pointer-events: none;
    top: 1em;
    font-size: 100%;
    left: 0;
    width: 3em;
    letter-spacing: -1px;
    -webkit-user-select: none;
    user-select: none;
    counter-reset: linenumber
}

.md-editor-scrn span[rn-wrapper]>span {
    display: block;
    pointer-events: none;
    counter-increment: linenumber
}

.md-editor-scrn span[rn-wrapper]>span:before {
    color: #999;
    display: block;
    padding-right: .5em;
    text-align: right;
    content: counter(linenumber)
}

.md-editor-scrn pre code {
    padding-left: 3.5em !important
}

.md-editor .md-editor-admonition-note {
    --md-admonition-color: #448aff;
    --md-admonition-bg-color: #d5e2f9
}

.md-editor .md-editor-admonition-abstract {
    --md-admonition-color: #02b1ff;
    --md-admonition-bg-color: #d1eefb
}

.md-editor .md-editor-admonition-info {
    --md-admonition-color: #333;
    --md-admonition-bg-color: #e3e3e3
}

.md-editor .md-editor-admonition-tip {
    --md-admonition-color: #666;
    --md-admonition-bg-color: #e6e6e6
}

.md-editor .md-editor-admonition-success {
    --md-admonition-color: #00c852;
    --md-admonition-bg-color: #c1f1d5
}

.md-editor .md-editor-admonition-question {
    --md-admonition-color: #f0b400;
    --md-admonition-bg-color: #fff1dd
}

.md-editor .md-editor-admonition-warning {
    --md-admonition-color: #ff9104;
    --md-admonition-bg-color: #ffe9cc
}

.md-editor .md-editor-admonition-failure {
    --md-admonition-color: #c2185b;
    --md-admonition-bg-color: #ffd9d9
}

.md-editor .md-editor-admonition-danger {
    --md-admonition-color: #ff5252;
    --md-admonition-bg-color: #ffe4e4
}

.md-editor .md-editor-admonition-bug {
    --md-admonition-color: #f60357;
    --md-admonition-bg-color: #ffd3e2
}

.md-editor .md-editor-admonition-example {
    --md-admonition-color: #7c4dff;
    --md-admonition-bg-color: #e3d8ff
}

.md-editor .md-editor-admonition-quote {
    --md-admonition-color: #9e9e9e;
    --md-admonition-bg-color: #f0f0f0
}

.md-editor .md-editor-admonition-hint {
    --md-admonition-color: #009688;
    --md-admonition-bg-color: #cdf4f0
}

.md-editor .md-editor-admonition-caution {
    --md-admonition-color: #ffa726;
    --md-admonition-bg-color: #ffe7c4
}

.md-editor .md-editor-admonition-error {
    --md-admonition-color: #d32f2f;
    --md-admonition-bg-color: #ffd8d8
}

.md-editor .md-editor-admonition-attention {
    --md-admonition-color: #455a64;
    --md-admonition-bg-color: #cbefff
}

.md-editor-dark .md-editor-admonition-note {
    --md-admonition-color: #1262e7;
    --md-admonition-bg-color: #021d4c
}

.md-editor-dark .md-editor-admonition-abstract {
    --md-admonition-color: #058dc9;
    --md-admonition-bg-color: #002433
}

.md-editor-dark .md-editor-admonition-info {
    --md-admonition-color: #999;
    --md-admonition-bg-color: #212121
}

.md-editor-dark .md-editor-admonition-tip {
    --md-admonition-color: #888;
    --md-admonition-bg-color: #191818
}

.md-editor-dark .md-editor-admonition-success {
    --md-admonition-color: #00c551;
    --md-admonition-bg-color: #003014
}

.md-editor-dark .md-editor-admonition-question {
    --md-admonition-color: #cd9a00;
    --md-admonition-bg-color: #311d00
}

.md-editor-dark .md-editor-admonition-warning {
    --md-admonition-color: #ed8500;
    --md-admonition-bg-color: #3c2200
}

.md-editor-dark .md-editor-admonition-failure {
    --md-admonition-color: #d5125f;
    --md-admonition-bg-color: #3f0000
}

.md-editor-dark .md-editor-admonition-danger {
    --md-admonition-color: #d80505;
    --md-admonition-bg-color: #390000
}

.md-editor-dark .md-editor-admonition-bug {
    --md-admonition-color: #da0d54;
    --md-admonition-bg-color: #390013
}

.md-editor-dark .md-editor-admonition-example {
    --md-admonition-color: #7443ff;
    --md-admonition-bg-color: #140045
}

.md-editor-dark .md-editor-admonition-quote {
    --md-admonition-color: #9e9e9e;
    --md-admonition-bg-color: #2b2b2b
}

.md-editor-dark .md-editor-admonition-hint {
    --md-admonition-color: #00ae9e;
    --md-admonition-bg-color: #00423b
}

.md-editor-dark .md-editor-admonition-caution {
    --md-admonition-color: #db8609;
    --md-admonition-bg-color: #573300
}

.md-editor-dark .md-editor-admonition-error {
    --md-admonition-color: #df1a1a;
    --md-admonition-bg-color: #440000
}

.md-editor-dark .md-editor-admonition-attention {
    --md-admonition-color: #0f8bc7;
    --md-admonition-bg-color: #00354d
}

.md-editor-preview .md-editor-admonition {
    background-color: var(--md-admonition-bg-color);
    border: 1px solid var(--md-admonition-color);
    border-radius: .5rem;
    color: var(--md-admonition-color);
    display: flow-root;
    font-size: 14px;
    font-weight: 400;
    margin: 1rem 0;
    padding: 1em 1em .5em;
    page-break-inside: avoid
}

.md-editor-preview .md-editor-admonition-title {
    border-top-left-radius: .5rem;
    border-top-right-radius: .5rem;
    margin: 0;
    padding: 0;
    position: relative;
    font-weight: 700
}

.md-editor-preview .md-editor-admonition p {
    margin: .5em 0;
    padding: 0
}

.md-editor-preview .md-editor-admonition p:first-of-type {
    margin-top: 0
}

.md-editor-preview .md-editor-admonition+p:empty,
.md-editor-preview .md-editor-admonition+p:empty+p:empty {
    display: none
}

.md-editor-mermaid {
    overflow-x: auto;
    display: none;
    text-align: center
}

[class=md-editor-mermaid][data-processed] {
    display: block
}

.prefix-katex-block {
    text-align: center;
    margin: 20px
}

.prefix-katex-inline,
.prefix-katex-block {
    display: none
}

.prefix-katex-inline[data-processed] {
    display: initial
}

.prefix-katex-block[data-processed] {
    display: block
}

.md-editor-preview .code-tabs {
    border-radius: 5px;
    margin: 20px 0
}

.md-editor-preview .code-tabs pre {
    margin: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.md-editor-preview .code-tabs pre:before {
    display: none
}

.md-editor-preview .code-tabs pre code {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.md-editor-preview .code-tabs pre code[language]:before {
    top: -23px;
    right: 46px
}

.md-editor-preview .code-tabs pre .copy-button {
    top: -29px;
    right: 16px
}

.md-editor-preview .code-tabs pre,
.md-editor-preview .code-tabs input {
    display: none
}

.md-editor-preview .code-tabs input:checked+pre {
    display: block
}

.md-editor-preview .code-tabs label {
    color: var(--md-theme-code-block-color)
}

.md-editor-preview .code-tabs input:checked+label {
    border-bottom: 1px solid;
    color: var(--md-theme-code-active-color)
}

.md-editor-preview .code-tabs ul {
    box-sizing: border-box;
    white-space: nowrap;
    overflow: auto;
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    background-color: var(--md-theme-code-block-bg-color);
    margin: 0;
    padding: 1em 1em 0;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px
}

.md-editor-preview .code-tabs li {
    line-height: 1;
    list-style: none;
    display: inline-block;
    position: relative;
    vertical-align: super;
    margin: 0
}

.md-editor-preview .code-tabs label {
    cursor: pointer;
    -webkit-user-select: none;
    user-select: none;
    display: inline-block;
    margin: 0 5px;
    font-size: 14px
}

.md-editor div.default-theme {
    --md-theme-code-inline-color: #3594f7;
    --md-theme-code-inline-bg-color: rgba(59, 170, 250, .1);
    --md-theme-code-block-color: #a9b7c6;
    --md-theme-code-block-bg-color: #282c34;
    --md-theme-code-before-bg-color: var(--md-theme-code-block-bg-color);
    --md-theme-code-copy-tips-color: #141414
}

.md-editor-dark div.default-theme {
    --md-theme-code-inline-color: #3594f7;
    --md-theme-code-inline-bg-color: rgba(59, 170, 250, .1);
    --md-theme-code-block-color: #a9b7c6;
    --md-theme-code-block-bg-color: #1a1a1a;
    --md-theme-code-before-bg-color: var(--md-theme-code-block-bg-color);
    --md-theme-code-copy-tips-color: inherit
}

.default-theme code {
    color: var(--md-theme-code-inline-color);
    background-color: var(--md-theme-code-inline-bg-color);
    display: inline-block;
    padding: 0 4px;
    border-radius: 2px;
    line-height: 22px;
    z-index: -1
}

.default-theme pre {
    position: relative;
    border-radius: 5px;
    box-shadow: #0005 0 2px 2px
}

.default-theme pre code {
    padding: 1em;
    background-color: var(--md-theme-code-block-bg-color);
    color: var(--md-theme-code-block-color);
    border-radius: 0 0 5px 5px
}

.default-theme pre code>* {
    line-height: 1.6
}

.default-theme pre code span[rn-wrapper] {
    top: calc(1em + 32px)
}

.default-theme pre:before {
    content: "";
    display: block;
    background: url(data:image/png;base64,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);
    height: 32px;
    width: 100%;
    background-size: 40px;
    background-repeat: no-repeat;
    background-color: var(--md-theme-code-before-bg-color);
    margin-bottom: 0;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    background-position: 10px 10px
}

.default-theme .code-tabs pre code span[rn-wrapper] {
    top: 1em
}

.default-theme h1,
.default-theme h2,
.default-theme h3,
.default-theme h4,
.default-theme h5,
.default-theme h6 {
    margin: 1.4em 0 .8em;
    font-weight: 700;
    color: var(--md-theme-color)
}

.default-theme h1 {
    font-size: 2em
}

.default-theme h2 {
    font-size: 1.5em
}

.default-theme h3 {
    font-size: 1.25em
}

.default-theme h4 {
    font-size: 1em
}

.default-theme h5 {
    font-size: .875em
}

.default-theme h6 {
    font-size: .85em
}

.default-theme img {
    margin: 0 auto;
    max-width: 100%;
    box-sizing: border-box;
    padding: 5px;
    border: 1px solid var(--md-theme-border-color);
    border-radius: 3px
}

.default-theme blockquote img {
    border-color: var(--md-theme-border-color-inset)
}

.default-theme a {
    /* color: var(--md-theme-link-color); */
    color: #05a23c;
    transition: color .3s
}

h4 {
    color:#9f1515!important;
}

em {
    color: #00711a;
}

.default-theme a:hover {
    color: var(--md-theme-link-hover-color)
}

.default-theme ol,
.default-theme ul {
    margin: .6em 0;
    padding-left: 1.6em
}

.default-theme ol li,
.default-theme ul li {
    line-height: 1.6;
    margin: .5em 0
}

.default-theme p {
    line-height: 1.6;
    margin: 0;
    padding: .5rem 0;
    overflow-wrap: normal;
    word-break: normal;
}

.default-theme p:empty {
    display: none
}

.default-theme blockquote {
    margin: 20px 0;
    padding: 0 1.2em;
    line-height: 2em;
    background-color: var(--md-theme-bg-color-inset);
    border-left: 5px solid #35b378;
    display: block
}

.md-editor default-theme {
    --md-theme-table-stripe-color: #fafafa
}

.md-editor-dark default-theme {
    --md-theme-table-stripe-color: #0c0c0c
}

.default-theme table {
    overflow: auto;
    border-spacing: 0;
    border-collapse: collapse;
    margin-bottom: 1em;
    margin-top: 1em
}

.default-theme table tr th,
.default-theme table tr td {
    word-wrap: break-word;
    padding: 8px 14px;
    border: 1px solid var(--md-theme-border-color)
}

.default-theme table tbody tr:nth-child(2n) {
    background-color: var(--md-theme-table-stripe-color)
}

.default-theme table tbody tr:hover {
    background-color: var(--md-theme-color-hover)
}

.default-theme blockquote table {
    line-height: initial
}

.default-theme blockquote table tr th,
.default-theme blockquote table tr td {
    border-color: var(--md-theme-border-color-inset)
}

.default-theme blockquote table tbody tr:nth-child(n) {
    background-color: inherit
}

.default-theme blockquote table tbody tr:hover {
    background-color: var(--md-theme-color-hover-inset)
}

.default-theme {
    color: var(--md-theme-color)
}

.default-theme ::-webkit-scrollbar {
    width: 6px;
    height: 6px
}

.default-theme ::-webkit-scrollbar-corner,
.default-theme ::-webkit-scrollbar-track {
    background-color: var(--md-theme-bg-color-scrollbar-track);
    border-radius: 2px
}

.default-theme ::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: var(--md-theme-bg-color-scrollbar-thumb)
}

.default-theme ::-webkit-scrollbar-button:vertical {
    display: none
}

.default-theme ::-webkit-scrollbar-thumb:vertical:hover {
    background-color: var(--md-theme-bg-color-scrollbar-thumb-hover)
}

.default-theme ::-webkit-scrollbar-thumb:vertical:active {
    background-color: var(--md-theme-bg-color-scrollbar-thumb-active)
}

.default-theme h1,
.default-theme h2,
.default-theme h3,
.default-theme h4,
.default-theme h5,
.default-theme h6 {
    position: relative;
    word-break: break-all
}

.default-theme h1 a,
.default-theme h2 a,
.default-theme h3 a,
.default-theme h4 a,
.default-theme h5 a,
.default-theme h6 a,
.default-theme h1 a:hover,
.default-theme h2 a:hover,
.default-theme h3 a:hover,
.default-theme h4 a:hover,
.default-theme h5 a:hover,
.default-theme h6 a:hover {
    color: inherit
}

.default-theme ol>li {
    list-style: decimal
}

.default-theme ul>li {
    list-style: inherit
}

.default-theme ol .task-list-item,
.default-theme ul .task-list-item {
    list-style-type: none
}

.default-theme ol .task-list-item input,
.default-theme ul .task-list-item input {
    margin-left: -1.5em;
    margin-right: .1em
}

.default-theme a {
    text-decoration: none
}

.default-theme pre,
.default-theme code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;
    font-size: 14px
}

.default-theme pre {
    margin: 20px 0
}

.default-theme pre code {
    display: block;
    line-height: 1;
    overflow: auto
}

.default-theme pre code .code-block {
    display: inline-block;
    width: 100%;
    overflow: auto;
    vertical-align: bottom
}

.default-theme pre code[language]:before {
    content: attr(language);
    font-size: 12px;
    position: absolute;
    top: 11px;
    right: 40px;
    line-height: 1;
    z-index: 1
}

.default-theme .copy-button {
    color: var(--md-theme-code-block-color);
    position: absolute;
    font-size: 12px;
    top: 5px;
    right: 7px;
    line-height: 1;
    cursor: pointer;
    z-index: 1
}

.default-theme .copy-button:before {
    content: attr(data-tips);
    color: var(--md-theme-code-copy-tips-color);
    background-color: var(--md-theme-code-copy-tips-bg-color);
    position: absolute;
    font-size: 12px;
    font-family: sans-serif;
    width: max-content;
    text-align: center;
    padding: 4px;
    border-radius: 2px;
    box-shadow: 0 0 2px #0003;
    left: 0;
    top: 50%;
    transform: translate(-100%, -50%)
}

.default-theme .copy-button:after {
    content: "";
    color: var(--md-theme-code-copy-tips-bg-color);
    position: absolute;
    width: 0;
    height: 0;
    border: 5px solid rgba(0, 0, 0, 0);
    border-right-width: 0;
    border-left-color: currentColor;
    left: -2px;
    top: 50%;
    transform: translateY(-50%);
    filter: drop-shadow(4px 0 2px rgba(0, 0, 0, .2))
}

.default-theme .copy-button:before,
.default-theme .copy-button:after {
    visibility: hidden;
    transition: .3s
}

.default-theme .copy-button:hover:before,
.default-theme .copy-button:hover:after {
    visibility: visible
}

.default-theme hr {
    height: 1px;
    margin: 10px 0;
    border: none;
    border-top: 1px solid var(--md-theme-border-color)
}

.default-theme figure {
    margin: 0 0 1em;
    display: inline-flex;
    flex-direction: column;
    text-align: center
}

.default-theme figure figcaption {
    color: var(--md-theme-color);
    font-size: .875em;
    margin-top: 5px
}

.default-theme .md-editor-mermaid {
    line-height: 1
}

.md-editor div.vuepress-theme {
    --md-theme-code-inline-color: #d63200;
    --md-theme-code-inline-bg-color: #f8f8f8;
    --md-theme-code-block-color: #747384;
    --md-theme-code-block-bg-color: #f8f8f8
}

.md-editor-dark div.vuepress-theme {
    --md-theme-code-inline-color: #e06c75;
    --md-theme-code-inline-bg-color: #1a1a1a;
    --md-theme-code-block-color: #999;
    --md-theme-code-block-bg-color: #1a1a1a
}

.vuepress-theme code {
    color: var(--md-theme-code-inline-color);
    padding: 3px 5px;
    margin: 0 2px;
    border-radius: 2px;
    background-color: var(--md-theme-code-inline-bg-color)
}

.vuepress-theme pre {
    border-radius: 2px;
    position: relative;
    font-size: .875em;
    margin: 1em 0;
    background-color: var(--md-theme-code-block-bg-color)
}

.vuepress-theme pre code {
    overflow-x: auto;
    color: var(--md-theme-code-block-color);
    white-space: pre;
    padding: 22px 1em;
    background-color: var(--md-theme-code-block-bg-color)
}

.vuepress-theme pre code>* {
    line-height: 1.6
}

.vuepress-theme pre code span[rn-wrapper] {
    top: 22px
}

.md-editor div.vuepress-theme {
    --md-theme-heading-color: #273849
}

.md-editor-dark div.vuepress-theme {
    --md-theme-heading-color: #999
}

.vuepress-theme h1,
.vuepress-theme h2,
.vuepress-theme h3,
.vuepress-theme h4,
.vuepress-theme h5,
.vuepress-theme h6 {
    font-weight: 600;
    color: var(--md-theme-heading-color);
    line-height: 1.45;
    position: relative;
    margin-top: 1em
}

.vuepress-theme h1 {
    font-size: 2.2em;
    margin: 1em 0
}

.vuepress-theme h2 {
    font-size: 1.65em;
    padding-bottom: .3em;
    border-bottom: 1px solid var(--md-theme-border-color)
}

.vuepress-theme h3 {
    line-height: 1.35em
}

.vuepress-theme img {
    max-width: 100%
}

.vuepress-theme a {
    color: #42b983;
    font-weight: 600
}

.vuepress-theme ul,
.vuepress-theme ol {
    position: relative;
    padding-left: 1.25em;
    line-height: 1.4em;
    margin: 1.2em 0;
    z-index: 1
}

.vuepress-theme ul li,
.vuepress-theme ol li {
    margin: 1.2em 0
}

.vuepress-theme p {
    word-spacing: .05em;
    line-height: 1.6em;
    margin: 1.2em 0;
    position: relative
}

.vuepress-theme p:empty {
    display: none
}

.vuepress-theme blockquote {
    margin: 2em 0;
    padding-left: 20px;
    border-left: 4px solid #42b983
}

.vuepress-theme blockquote p {
    margin-left: 0;
    margin-top: 1.2em;
    margin-bottom: 0;
    padding: 0
}

.md-editor div.vuepress-theme {
    --md-theme-table-border-color: #dfe2e5;
    --md-theme-table-bg-color: #f6f8fa
}

.md-editor-dark div.vuepress-theme {
    --md-theme-table-border-color: #2d2d2d;
    --md-theme-table-bg-color: #0c0c0c
}

.vuepress-theme table {
    border-collapse: collapse;
    margin: 1rem 0;
    display: block;
    overflow-x: auto
}

.vuepress-theme table tr {
    border-top: 1px solid var(--md-theme-table-border-color)
}

.vuepress-theme table tr th,
.vuepress-theme table tr td {
    border: 1px solid var(--md-theme-table-border-color);
    padding: .6em 1em
}

.vuepress-theme table tr:nth-child(2n) {
    background-color: var(--md-theme-table-bg-color)
}

.md-editor .vuepress-theme {
    --md-theme-color: #304455
}

.md-editor-dark .vuepress-theme {
    --md-theme-color: #999
}

.vuepress-theme {
    font-size: 16px;
    color: var(--md-theme-color)
}

.vuepress-theme ::-webkit-scrollbar {
    width: 6px;
    height: 6px
}

.vuepress-theme ::-webkit-scrollbar-corner,
.vuepress-theme ::-webkit-scrollbar-track {
    background-color: var(--md-theme-bg-color-scrollbar-track);
    border-radius: 2px
}

.vuepress-theme ::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: var(--md-theme-bg-color-scrollbar-thumb)
}

.vuepress-theme ::-webkit-scrollbar-button:vertical {
    display: none
}

.vuepress-theme ::-webkit-scrollbar-thumb:vertical:hover {
    background-color: var(--md-theme-bg-color-scrollbar-thumb-hover)
}

.vuepress-theme ::-webkit-scrollbar-thumb:vertical:active {
    background-color: var(--md-theme-bg-color-scrollbar-thumb-active)
}

.vuepress-theme h1,
.vuepress-theme h2,
.vuepress-theme h3,
.vuepress-theme h4,
.vuepress-theme h5,
.vuepress-theme h6 {
    position: relative;
    word-break: break-all
}

.vuepress-theme h1 a,
.vuepress-theme h2 a,
.vuepress-theme h3 a,
.vuepress-theme h4 a,
.vuepress-theme h5 a,
.vuepress-theme h6 a,
.vuepress-theme h1 a:hover,
.vuepress-theme h2 a:hover,
.vuepress-theme h3 a:hover,
.vuepress-theme h4 a:hover,
.vuepress-theme h5 a:hover,
.vuepress-theme h6 a:hover {
    color: inherit
}

.vuepress-theme ol>li {
    list-style: decimal
}

.vuepress-theme ul>li {
    list-style: inherit
}

.vuepress-theme ol .task-list-item,
.vuepress-theme ul .task-list-item {
    list-style-type: none
}

.vuepress-theme ol .task-list-item input,
.vuepress-theme ul .task-list-item input {
    margin-left: -1.5em;
    margin-right: .1em
}

.vuepress-theme a {
    text-decoration: none
}

.vuepress-theme pre,
.vuepress-theme code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;
    font-size: 14px
}

.vuepress-theme pre {
    margin: 20px 0
}

.vuepress-theme pre code {
    display: block;
    line-height: 1;
    overflow: auto
}

.vuepress-theme pre code .code-block {
    display: inline-block;
    width: 100%;
    overflow: auto;
    vertical-align: bottom
}

.vuepress-theme pre code[language]:before {
    content: attr(language);
    font-size: 12px;
    position: absolute;
    top: 11px;
    right: 40px;
    line-height: 1;
    z-index: 1
}

.vuepress-theme .copy-button {
    color: var(--md-theme-code-block-color);
    position: absolute;
    font-size: 12px;
    top: 5px;
    right: 7px;
    line-height: 1;
    cursor: pointer;
    z-index: 1
}

.vuepress-theme .copy-button:before {
    content: attr(data-tips);
    color: var(--md-theme-code-copy-tips-color);
    background-color: var(--md-theme-code-copy-tips-bg-color);
    position: absolute;
    font-size: 12px;
    font-family: sans-serif;
    width: max-content;
    text-align: center;
    padding: 4px;
    border-radius: 2px;
    box-shadow: 0 0 2px #0003;
    left: 0;
    top: 50%;
    transform: translate(-100%, -50%)
}

.vuepress-theme .copy-button:after {
    content: "";
    color: var(--md-theme-code-copy-tips-bg-color);
    position: absolute;
    width: 0;
    height: 0;
    border: 5px solid rgba(0, 0, 0, 0);
    border-right-width: 0;
    border-left-color: currentColor;
    left: -2px;
    top: 50%;
    transform: translateY(-50%);
    filter: drop-shadow(4px 0 2px rgba(0, 0, 0, .2))
}

.vuepress-theme .copy-button:before,
.vuepress-theme .copy-button:after {
    visibility: hidden;
    transition: .3s
}

.vuepress-theme .copy-button:hover:before,
.vuepress-theme .copy-button:hover:after {
    visibility: visible
}

.vuepress-theme hr {
    height: 1px;
    margin: 10px 0;
    border: none;
    border-top: 1px solid var(--md-theme-border-color)
}

.vuepress-theme figure {
    margin: 0 0 1em;
    display: inline-flex;
    flex-direction: column;
    text-align: center
}

.vuepress-theme figure figcaption {
    color: var(--md-theme-color);
    font-size: .875em;
    margin-top: 5px
}

.vuepress-theme .md-editor-mermaid {
    line-height: 1
}

.vuepress-theme em {
    color: #4f5959;
    padding: 0 6px 0 4px
}

.md-editor div.github-theme {
    --md-theme-code-inline-color: inherit;
    --md-theme-code-inline-bg-color: #eff1f2;
    --md-theme-code-block-color: inherit;
    --md-theme-code-block-bg-color: #f6f8fa
}

.md-editor-dark div.github-theme {
    --md-theme-code-inline-color: #c9d1d9;
    --md-theme-code-inline-bg-color: #2d3339;
    --md-theme-code-block-color: #a9b7c6;
    --md-theme-code-block-bg-color: #161b22
}

.github-theme code {
    padding: .2em .4em;
    margin: 0;
    color: var(--md-theme-code-inline-color);
    background-color: var(--md-theme-code-inline-bg-color);
    border-radius: 6px
}

.github-theme pre {
    border-radius: 6px;
    position: relative
}

.github-theme pre code {
    padding: 22px 1em;
    margin-bottom: 0;
    word-break: normal;
    letter-spacing: 1px;
    color: var(--md-theme-code-block-color);
    background-color: var(--md-theme-code-block-bg-color)
}

.github-theme pre code>* {
    line-height: 1.6
}

.github-theme pre code span[rn-wrapper] {
    top: 22px
}

.md-editor div.github-theme {
    --md-theme-heading-color: inherit;
    --md-theme-heading-6-color: #2d3339;
    --md-theme-heading-border-color: #d9dee4
}

.md-editor-dark div.github-theme {
    --md-theme-heading-color: #c9d1d9;
    --md-theme-heading-6-color: #768390;
    --md-theme-heading-border-color: #373e47
}

.github-theme h1,
.github-theme h2,
.github-theme h3,
.github-theme h4,
.github-theme h5,
.github-theme h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
    /* color: var(--md-theme-heading-color) */
}

.github-theme h1 {
    padding-bottom: .3em;
    font-size: 2em;
    border-bottom: 1px solid var(--md-theme-heading-border-color)
}

.github-theme h2 {
    padding-bottom: .3em;
    font-size: 1.5em;
    border-bottom: 1px solid var(--md-theme-heading-border-color)
}

.github-theme h3 {
    font-size: 1.25em
}

.github-theme h4 {
    font-size: 1em
}

.github-theme h5 {
    font-size: .875em
}

.github-theme h6 {
    font-size: .85em;
    color: var(--md-theme-heading-6-color)
}

.md-editor div.github-theme {
    --md-theme-heading-bg-color: #fff
}

.md-editor-dark div.github-theme {
    --md-theme-heading-bg-color: #22272e
}

.github-theme img {
    max-width: 100%;
    box-sizing: content-box;
    background-color: var(--md-theme-heading-bg-color)
}

.github-theme a {
    color: #539bf5
}

.github-theme a:hover {
    text-decoration: underline
}

.github-theme ol,
.github-theme ul {
    padding-left: 2em
}

.github-theme ol li+li,
.github-theme ul li+li {
    margin-top: .25em
}

.github-theme p:empty {
    display: none
}

.md-editor div.github-theme {
    --md-theme-quote-color: #57606a;
    --md-theme-quote-border-color: #d0d7de
}

.md-editor-dark div.github-theme {
    --md-theme-quote-color: #8b949e;
    --md-theme-quote-border-color: #444c56
}

.github-theme blockquote {
    padding: 0 1em;
    color: var(--md-theme-quote-color);
    border-left: .25em solid var(--md-theme-quote-border-color)
}

.md-editor div.github-theme {
    --md-theme-table-stripe-color: #f7f8fa;
    --md-theme-table-tr-bg-color: #fff;
    --md-theme-table-tr-border-color: #d8dee4;
    --md-theme-table-td-border-color: #d0d7de
}

.md-editor-dark div.github-theme {
    --md-theme-table-stripe-color: #161b22;
    --md-theme-table-tr-bg-color: transparent;
    --md-theme-table-tr-border-color: #808080;
    --md-theme-table-td-border-color: #30363d
}

.github-theme table {
    display: block;
    max-width: 100%;
    overflow: auto;
    border-spacing: 0;
    border-collapse: collapse
}

.github-theme table tr {
    background-color: var(--md-theme-table-tr-bg-color);
    border-top: 1px solid var(--md-theme-table-tr-border-color)
}

.github-theme table tr th,
.github-theme table tr td {
    padding: 6px 13px;
    border: 1px solid var(--md-theme-table-td-border-color)
}

.github-theme table tr:nth-child(2n) {
    background-color: var(--md-theme-table-stripe-color)
}

.md-editor .github-theme {
    --md-theme-color: #222
}

.md-editor-dark .github-theme {
    --md-theme-color: #c9d1d9
}

.github-theme {
    line-height: 1.5;
    color: var(--md-theme-color)
}

.github-theme ::-webkit-scrollbar {
    width: 6px;
    height: 6px
}

.github-theme ::-webkit-scrollbar-corner,
.github-theme ::-webkit-scrollbar-track {
    background-color: var(--md-theme-bg-color-scrollbar-track);
    border-radius: 2px
}

.github-theme ::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: var(--md-theme-bg-color-scrollbar-thumb)
}

.github-theme ::-webkit-scrollbar-button:vertical {
    display: none
}

.github-theme ::-webkit-scrollbar-thumb:vertical:hover {
    background-color: var(--md-theme-bg-color-scrollbar-thumb-hover)
}

.github-theme ::-webkit-scrollbar-thumb:vertical:active {
    background-color: var(--md-theme-bg-color-scrollbar-thumb-active)
}

.github-theme h1,
.github-theme h2,
.github-theme h3,
.github-theme h4,
.github-theme h5,
.github-theme h6 {
    position: relative;
    word-break: break-all
}

.github-theme h1 a,
.github-theme h2 a,
.github-theme h3 a,
.github-theme h4 a,
.github-theme h5 a,
.github-theme h6 a,
.github-theme h1 a:hover,
.github-theme h2 a:hover,
.github-theme h3 a:hover,
.github-theme h4 a:hover,
.github-theme h5 a:hover,
.github-theme h6 a:hover {
    color: inherit
}

.github-theme ol>li {
    list-style: decimal
}

.github-theme ul>li {
    list-style: inherit
}

.github-theme ol .task-list-item,
.github-theme ul .task-list-item {
    list-style-type: none
}

.github-theme ol .task-list-item input,
.github-theme ul .task-list-item input {
    margin-left: -1.5em;
    margin-right: .1em
}

.github-theme a {
    text-decoration: none
}

.github-theme pre,
.github-theme code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;
    font-size: 14px
}

.github-theme pre {
    margin: 20px 0
}

.github-theme pre code {
    display: block;
    line-height: 1;
    overflow: auto
}

.github-theme pre code .code-block {
    display: inline-block;
    width: 100%;
    overflow: auto;
    vertical-align: bottom
}

.github-theme pre code[language]:before {
    content: attr(language);
    font-size: 12px;
    position: absolute;
    top: 11px;
    right: 40px;
    line-height: 1;
    z-index: 1
}

.github-theme .copy-button {
    color: var(--md-theme-code-block-color);
    position: absolute;
    font-size: 12px;
    top: 5px;
    right: 7px;
    line-height: 1;
    cursor: pointer;
    z-index: 1
}

.github-theme .copy-button:before {
    content: attr(data-tips);
    color: var(--md-theme-code-copy-tips-color);
    background-color: var(--md-theme-code-copy-tips-bg-color);
    position: absolute;
    font-size: 12px;
    font-family: sans-serif;
    width: max-content;
    text-align: center;
    padding: 4px;
    border-radius: 2px;
    box-shadow: 0 0 2px #0003;
    left: 0;
    top: 50%;
    transform: translate(-100%, -50%)
}

.github-theme .copy-button:after {
    content: "";
    color: var(--md-theme-code-copy-tips-bg-color);
    position: absolute;
    width: 0;
    height: 0;
    border: 5px solid rgba(0, 0, 0, 0);
    border-right-width: 0;
    border-left-color: currentColor;
    left: -2px;
    top: 50%;
    transform: translateY(-50%);
    filter: drop-shadow(4px 0 2px rgba(0, 0, 0, .2))
}

.github-theme .copy-button:before,
.github-theme .copy-button:after {
    visibility: hidden;
    transition: .3s
}

.github-theme .copy-button:hover:before,
.github-theme .copy-button:hover:after {
    visibility: visible
}

.github-theme hr {
    height: 1px;
    margin: 10px 0;
    border: none;
    border-top: 1px solid var(--md-theme-border-color)
}

.github-theme figure {
    margin: 0 0 1em;
    display: inline-flex;
    flex-direction: column;
    text-align: center
}

.github-theme figure figcaption {
    color: var(--md-theme-color);
    font-size: .875em;
    margin-top: 5px
}

.github-theme .md-editor-mermaid {
    line-height: 1
}

.github-theme p,
.github-theme blockquote,
.github-theme ul,
.github-theme ol,
.github-theme dl,
.github-theme table,
.github-theme pre,
.github-theme details {
    margin-top: 0;
    margin-bottom: 16px
}

.cyanosis-theme code {
    padding: .065em .4em;
    font-family: Menlo, Monaco, Consolas, Courier New, monospace;
    color: var(--md-theme-code-color);
    overflow-x: auto;
    background-color: var(--md-theme-code-bg-color);
    border-radius: 2px
}

.cyanosis-theme code::selection {
    background-color: var(--md-theme-slct-codebg-color)
}

.cyanosis-theme pre {
    font-family: Menlo, Monaco, Consolas, Courier New, monospace;
    position: relative
}

.cyanosis-theme pre code {
    padding: 22px 12px;
    margin: 0;
    color: var(--md-theme-base-color);
    word-break: normal;
    overflow-x: auto;
    background: var(--md-theme-code-block-bg-color)
}

.cyanosis-theme pre code::selection {
    background-color: var(--md-theme-slct-prebg-color)
}

.cyanosis-theme pre code>* {
    line-height: 1.75
}

.cyanosis-theme pre code::-webkit-scrollbar {
    width: 4px;
    height: 4px
}

.cyanosis-theme pre code::-webkit-scrollbar-track {
    background-color: var(--md-theme-border-color)
}

.cyanosis-theme pre code::-webkit-scrollbar-thumb {
    background-color: var(--md-theme-strong-color);
    border-radius: 10px
}

.cyanosis-theme pre code span[rn-wrapper] {
    top: 22px
}

.cyanosis-theme pre .copy-button {
    color: var(--md-theme-base-color)
}

.md-editor div.cyanosis-theme,
.md-editor-dark div.cyanosis-theme {
    --md-theme-heading-color: var(--md-theme-title-color)
}

.cyanosis-theme h1 {
    padding-bottom: 4px;
    margin-top: 36px;
    margin-bottom: 10px;
    font-size: 30px;
    line-height: 1.5;
    color: var(--md-theme-heading-color);
    transition: color .35s
}

.cyanosis-theme h2 {
    position: relative;
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 10px;
    margin-top: 36px;
    margin-bottom: 10px;
    font-size: 24px;
    line-height: 1.5;
    color: var(--md-theme-heading-color);
    border-bottom: 1px solid var(--md-theme-border-color-2);
    transition: color .35s
}

.cyanosis-theme h2:before {
    content: "「";
    position: absolute;
    top: -6px;
    left: -14px
}

.cyanosis-theme h2:after {
    content: "」";
    position: relative;
    top: 6px;
    right: auto
}

.cyanosis-theme h3 {
    position: relative;
    padding-bottom: 0;
    margin-top: 30px;
    margin-bottom: 10px;
    font-size: 20px;
    line-height: 1.5;
    color: var(--md-theme-heading-color);
    padding-left: 6px;
    transition: color .35s
}

.cyanosis-theme h3:before {
    content: "»";
    padding-right: 6px;
    color: var(--md-theme-strong-color)
}

.cyanosis-theme h4 {
    padding-bottom: 0;
    margin-top: 24px;
    margin-bottom: 10px;
    font-size: 16px;
    line-height: 1.5;
    color: var(--md-theme-heading-color);
    padding-left: 6px;
    transition: color .35s
}

.cyanosis-theme h5 {
    padding-bottom: 0;
    margin-top: 18px;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.5;
    color: var(--md-theme-heading-color);
    padding-left: 6px;
    transition: color .35s
}

.cyanosis-theme h6 {
    padding-bottom: 0;
    margin-top: 12px;
    margin-bottom: 10px;
    font-size: 12px;
    line-height: 1.5;
    color: var(--md-theme-heading-color);
    padding-left: 6px;
    transition: color .35s
}

.cyanosis-theme h1::selection,
.cyanosis-theme h2::selection,
.cyanosis-theme h3::selection,
.cyanosis-theme h4::selection,
.cyanosis-theme h5::selection,
.cyanosis-theme h6::selection {
    color: var(--md-theme-slct-title-color);
    background-color: var(--md-theme-slct-titlebg-color)
}

@media (max-width: 720px) {
    .cyanosis-theme h1 {
        font-size: 24px
    }

    .cyanosis-theme h2 {
        font-size: 20px
    }

    .cyanosis-theme h3 {
        font-size: 18px
    }
}

.cyanosis-theme img {
    max-width: 100%
}

.cyanosis-theme a {
    position: relative;
    display: inline-block;
    text-decoration: none;
    color: var(--md-theme-link-color);
    border-bottom: 1px solid var(--md-theme-border-color)
}

.cyanosis-theme a:hover {
    color: var(--md-theme-linkh-color);
    border-bottom-color: var(--md-theme-linkh-color)
}

.cyanosis-theme a:active {
    color: var(--md-theme-linkh-color)
}

.cyanosis-theme a:after {
    position: absolute;
    content: "";
    top: 100%;
    left: 0;
    width: 100%;
    opacity: 0;
    border-bottom: 1px solid var(--md-theme-border-color);
    transition: top .3s, opacity .3s;
    transform: translateZ(0)
}

.cyanosis-theme a:hover:after {
    top: 0;
    opacity: 1;
    border-bottom-color: var(--md-theme-linkh-color)
}

.cyanosis-theme ol,
.cyanosis-theme ul {
    padding-left: 28px
}

.cyanosis-theme ol li,
.cyanosis-theme ul li {
    margin-bottom: 0;
    list-style: inherit
}

.cyanosis-theme ol li .task-list-item,
.cyanosis-theme ul li .task-list-item {
    list-style: none
}

.cyanosis-theme ol li .task-list-item ul,
.cyanosis-theme ol li .task-list-item ol,
.cyanosis-theme ul li .task-list-item ul,
.cyanosis-theme ul li .task-list-item ol {
    margin-top: 0
}

.cyanosis-theme ol ul,
.cyanosis-theme ol ol,
.cyanosis-theme ul ul,
.cyanosis-theme ul ol {
    margin-top: 4px
}

.cyanosis-theme ol li {
    padding-left: 6px
}

.cyanosis-theme ol li::selection,
.cyanosis-theme ul li::selection {
    color: var(--md-theme-slct-text-color);
    background-color: var(--md-theme-slct-bg-color)
}

.cyanosis-theme .task-list-item-checkbox {
    position: relative
}

.cyanosis-theme .contains-task-list input[type=checkbox]:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: inherit;
    height: inherit;
    background: #f0f8ff;
    border: 1px solid #add6ff;
    border-radius: 2px;
    box-sizing: border-box;
    z-index: 1
}

.cyanosis-theme .contains-task-list input[type=checkbox]:after {
    content: "✓";
    position: absolute;
    top: -12px;
    left: 0;
    right: 0;
    bottom: 0;
    width: 0;
    height: 0;
    color: #f55;
    font-size: 20px;
    font-weight: 700;
    z-index: 2
}

.cyanosis-theme p {
    line-height: inherit;
    margin-top: 16px;
    margin-bottom: 16px
}

.cyanosis-theme p::selection {
    color: var(--md-theme-slct-text-color);
    background-color: var(--md-theme-slct-bg-color)
}

.cyanosis-theme blockquote {
    color: var(--md-theme-blockquote-color);
    border-left: 4px solid var(--md-theme-strong-color);
    background-color: var(--md-theme-blockquote-bg-color);
    padding: 1px 20px;
    margin: 22px 0;
    transition: color .35s
}

.cyanosis-theme blockquote:after {
    display: block;
    content: ""
}

.cyanosis-theme blockquote>p {
    margin: 10px 0
}

.cyanosis-theme blockquote>b,
.cyanosis-theme blockquote>strong {
    color: var(--md-theme-strong-color)
}

.cyanosis-theme table {
    display: inline-block !important;
    width: auto;
    max-width: 100%;
    overflow: auto;
    border: 1px solid var(--md-theme-table-border-color);
    border-spacing: 0;
    border-collapse: collapse
}

.cyanosis-theme table thead {
    color: #000;
    text-align: left;
    background: #f6f6f6
}

.cyanosis-theme table tr:nth-child(2n) {
    background-color: var(--md-theme-table-tr-nc-color)
}

.cyanosis-theme table tr:hover {
    background-color: var(--md-theme-table-trh-color)
}

.cyanosis-theme table th,
.cyanosis-theme table td {
    padding: 12px 8px;
    line-height: 24px;
    border: 1px solid var(--md-theme-table-border-color)
}

.cyanosis-theme table th {
    color: var(--md-theme-table-tht-color);
    background-color: var(--md-theme-table-th-color)
}

.cyanosis-theme table td {
    min-width: 120px
}

.cyanosis-theme table thead th::selection {
    background-color: #0000
}

.cyanosis-theme table tbody td::selection {
    background-color: var(--md-theme-slct-bg-color)
}

.md-editor .cyanosis-theme {
    --md-theme-base-color: #353535;
    --md-theme-title-color: #005bb7;
    --md-theme-strong-color: #2196f3;
    --md-theme-em-color: #4fc3f7;
    --md-theme-del-color: #ccc;
    --md-theme-link-color: #3da8f5;
    --md-theme-linkh-color: #007fff;
    --md-theme-border-color: #bedcff;
    --md-theme-border-color-2: #ececec;
    --md-theme-bg-color: #fff;
    --md-theme-blockquote-color: #8c8c8c;
    --md-theme-blockquote-bg-color: #f0fdff;
    --md-theme-code-color: #c2185b;
    --md-theme-code-bg-color: #fff4f4;
    --md-theme-code-block-bg-color: #f8f8f8;
    --md-theme-table-border-color: #c3e0fd;
    --md-theme-table-th-color: #dff0ff;
    --md-theme-table-tht-color: #005bb7;
    --md-theme-table-tr-nc-color: #f7fbff;
    --md-theme-table-trh-color: #e0edf7;
    --md-theme-slct-title-color: #005bb7;
    --md-theme-slct-titlebg-color: rgba(175, 207, 247, .25);
    --md-theme-slct-text-color: #c80000;
    --md-theme-slct-bg-color: rgba(175, 207, 247, .25);
    --md-theme-slct-del-color: #999;
    --md-theme-slct-elbg-color: #e8ebec;
    --md-theme-slct-codebg-color: #ffeaeb;
    --md-theme-slct-prebg-color: rgba(160, 200, 255, .25)
}

.md-editor-dark .cyanosis-theme {
    --md-theme-base-color: #cacaca;
    --md-theme-title-color: #ddd;
    --md-theme-strong-color: #fe9900;
    --md-theme-em-color: #ffd28e;
    --md-theme-del-color: #ccc;
    --md-theme-link-color: #ffb648;
    --md-theme-linkh-color: #fe9900;
    --md-theme-border-color: #ffe3ba;
    --md-theme-border-color-2: #ffcb7b;
    --md-theme-bg-color: #2f2f2f;
    --md-theme-blockquote-color: #c7c7c7;
    --md-theme-blockquote-bg-color: rgba(255, 199, 116, .1);
    --md-theme-code-color: #000;
    --md-theme-code-bg-color: #ffcb7b;
    --md-theme-code-block-bg-color: rgba(30, 25, 18, .5);
    --md-theme-table-border-color: #fe9900;
    --md-theme-table-th-color: #ffb648;
    --md-theme-table-tht-color: #000;
    --md-theme-table-tr-nc-color: #6d5736;
    --md-theme-table-trh-color: #947443;
    --md-theme-slct-title-color: #000;
    --md-theme-slct-titlebg-color: #fe9900;
    --md-theme-slct-text-color: #00c888;
    --md-theme-slct-bg-color: rgba(175, 207, 247, .25);
    --md-theme-slct-del-color: #999;
    --md-theme-slct-elbg-color: #000;
    --md-theme-slct-codebg-color: #ffcb7b;
    --md-theme-slct-prebg-color: rgba(160, 200, 255, .25)
}

.cyanosis-theme {
    color: var(--md-theme-color);
    word-break: break-word;
    line-height: 1.75;
    font-weight: 400;
    overflow-x: hidden;
    color: var(--md-theme-base-color);
    transition: color .35s
}

.cyanosis-theme ::-webkit-scrollbar {
    width: 6px;
    height: 6px
}

.cyanosis-theme ::-webkit-scrollbar-corner,
.cyanosis-theme ::-webkit-scrollbar-track {
    background-color: var(--md-theme-bg-color-scrollbar-track);
    border-radius: 2px
}

.cyanosis-theme ::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: var(--md-theme-bg-color-scrollbar-thumb)
}

.cyanosis-theme ::-webkit-scrollbar-button:vertical {
    display: none
}

.cyanosis-theme ::-webkit-scrollbar-thumb:vertical:hover {
    background-color: var(--md-theme-bg-color-scrollbar-thumb-hover)
}

.cyanosis-theme ::-webkit-scrollbar-thumb:vertical:active {
    background-color: var(--md-theme-bg-color-scrollbar-thumb-active)
}

.cyanosis-theme h1,
.cyanosis-theme h2,
.cyanosis-theme h3,
.cyanosis-theme h4,
.cyanosis-theme h5,
.cyanosis-theme h6 {
    position: relative;
    word-break: break-all
}

.cyanosis-theme h1 a,
.cyanosis-theme h2 a,
.cyanosis-theme h3 a,
.cyanosis-theme h4 a,
.cyanosis-theme h5 a,
.cyanosis-theme h6 a,
.cyanosis-theme h1 a:hover,
.cyanosis-theme h2 a:hover,
.cyanosis-theme h3 a:hover,
.cyanosis-theme h4 a:hover,
.cyanosis-theme h5 a:hover,
.cyanosis-theme h6 a:hover {
    color: inherit
}

.cyanosis-theme ol>li {
    list-style: decimal
}

.cyanosis-theme ul>li {
    list-style: inherit
}

.cyanosis-theme ol .task-list-item,
.cyanosis-theme ul .task-list-item {
    list-style-type: none
}

.cyanosis-theme ol .task-list-item input,
.cyanosis-theme ul .task-list-item input {
    margin-left: -1.5em;
    margin-right: .1em
}

.cyanosis-theme a {
    text-decoration: none
}

.cyanosis-theme pre,
.cyanosis-theme code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;
    font-size: 14px
}

.cyanosis-theme pre {
    margin: 20px 0
}

.cyanosis-theme pre code {
    display: block;
    line-height: 1;
    overflow: auto
}

.cyanosis-theme pre code .code-block {
    display: inline-block;
    width: 100%;
    overflow: auto;
    vertical-align: bottom
}

.cyanosis-theme pre code[language]:before {
    content: attr(language);
    font-size: 12px;
    position: absolute;
    top: 11px;
    right: 40px;
    line-height: 1;
    z-index: 1
}

.cyanosis-theme .copy-button {
    color: var(--md-theme-code-block-color);
    position: absolute;
    font-size: 12px;
    top: 5px;
    right: 7px;
    line-height: 1;
    cursor: pointer;
    z-index: 1
}

.cyanosis-theme .copy-button:before {
    content: attr(data-tips);
    color: var(--md-theme-code-copy-tips-color);
    background-color: var(--md-theme-code-copy-tips-bg-color);
    position: absolute;
    font-size: 12px;
    font-family: sans-serif;
    width: max-content;
    text-align: center;
    padding: 4px;
    border-radius: 2px;
    box-shadow: 0 0 2px #0003;
    left: 0;
    top: 50%;
    transform: translate(-100%, -50%)
}

.cyanosis-theme .copy-button:after {
    content: "";
    color: var(--md-theme-code-copy-tips-bg-color);
    position: absolute;
    width: 0;
    height: 0;
    border: 5px solid rgba(0, 0, 0, 0);
    border-right-width: 0;
    border-left-color: currentColor;
    left: -2px;
    top: 50%;
    transform: translateY(-50%);
    filter: drop-shadow(4px 0 2px rgba(0, 0, 0, .2))
}

.cyanosis-theme .copy-button:before,
.cyanosis-theme .copy-button:after {
    visibility: hidden;
    transition: .3s
}

.cyanosis-theme .copy-button:hover:before,
.cyanosis-theme .copy-button:hover:after {
    visibility: visible
}

.cyanosis-theme hr {
    height: 1px;
    margin: 10px 0;
    border: none;
    border-top: 1px solid var(--md-theme-border-color)
}

.cyanosis-theme figure {
    margin: 0 0 1em;
    display: inline-flex;
    flex-direction: column;
    text-align: center
}

.cyanosis-theme figure figcaption {
    color: var(--md-theme-color);
    font-size: .875em;
    margin-top: 5px
}

.cyanosis-theme .md-editor-mermaid {
    line-height: 1
}

.cyanosis-theme hr {
    position: relative;
    width: 98%;
    height: 1px;
    margin-top: 32px;
    margin-bottom: 32px;
    background-image: linear-gradient(90deg, var(--md-theme-link-color), rgba(255, 0, 0, .3), rgba(37, 163, 65, .3), rgba(255, 0, 0, .3), var(--md-theme-link-color));
    border-width: 0;
    overflow: visible
}

.cyanosis-theme b,
.cyanosis-theme strong {
    color: var(--md-theme-strong-color)
}

.cyanosis-theme i,
.cyanosis-theme em {
    color: var(--md-theme-em-color)
}

.cyanosis-theme del {
    color: var(--md-theme-del-color)
}

.cyanosis-theme details>summary {
    outline: none;
    color: var(--md-theme-title-color);
    font-size: 20px;
    font-weight: bolder;
    border-bottom: 1px solid var(--md-theme-border-color);
    cursor: pointer
}

.cyanosis-theme details>p {
    padding: 10px 20px;
    margin: 10px 0 0;
    color: #666;
    background-color: var(--md-theme-blockquote-bg-color);
    border: 2px dashed var(--md-theme-strong-color)
}

.cyanosis-theme a::selection,
.cyanosis-theme b::selection,
.cyanosis-theme strong::selection,
.cyanosis-theme i::selection,
.cyanosis-theme em::selection {
    background-color: var(--md-theme-slct-elbg-color)
}

.cyanosis-theme del::selection {
    color: var(--md-theme-slct-del-color);
    background-color: var(--md-theme-slct-elbg-color)
}

.md-editor div.mk-cute-theme,
.md-editor-dark div.mk-cute-theme {
    --md-theme-code-inline-color: #4ec9b0;
    --md-theme-code-inline-bg-color: #282c34;
    --md-theme-code-block-color: #4ec9b0;
    --md-theme-code-block-bg-color: #282c34
}

.mk-cute-theme code {
    font-family: Menlo, Monaco, Consolas, Courier New, monospace;
    border-radius: 2px;
    overflow-x: auto;
    background-color: var(--md-theme-code-block-bg-color);
    color: var(--md-theme-code-inline-color);
    padding: .14em .46em;
    margin: 0 4px
}

.mk-cute-theme pre {
    position: relative
}

.mk-cute-theme pre code {
    font-family: Menlo, Monaco, Consolas, Courier New, monospace;
    border-radius: 10px;
    padding: 22px;
    margin: 0;
    word-break: normal;
    display: block;
    overflow-x: auto;
    color: var(--md-theme-code-block-color);
    background: var(--md-theme-code-block-bg-color)
}

.mk-cute-theme pre code>* {
    line-height: 1.75
}

.mk-cute-theme pre code span[rn-wrapper] {
    top: 22px
}

.mk-cute-theme .code-tabs {
    border-radius: 10px
}

.mk-cute-theme .code-tabs ul {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px
}

.mk-cute-theme h1,
.mk-cute-theme h2,
.mk-cute-theme h3,
.mk-cute-theme h4,
.mk-cute-theme h5,
.mk-cute-theme h6 {
    color: #36ace1
}

.mk-cute-theme h1:before,
.mk-cute-theme h2:before,
.mk-cute-theme h3:before,
.mk-cute-theme h4:before,
.mk-cute-theme h5:before,
.mk-cute-theme h6:before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAF8UlEQVRIS71Wa2wUVRT+7r0zu9t2t/RBaSioPCpYbIUfaEIQUogSAwZDAlUSGwgg/CBATExMCJH1D2hIfOEjFEUEhViCgBgIUCH44OkjPAMGBVqhpUCfW3Zn5z7MuQOE0hYxMdxJdmd25s53vnO+851leMCLPWA8/CfA2TsvL8n7q+nTFfNLG+4VqInHOeJLDQMzdz/3r4DGGDb9lxu+aPcE7U61JHDMDePcuv0O21ShugOefqDdtBie3Dk6K/O+Ab+qOjJiz7Ahv6c8hbDDwRiQlgYGDOcaWyEcjg8On+j71IpJndjGt9XO+jM7+pkywNvbazIfercieSdoJ4bE5sWjyZqMpDdeaQNXMNC34ME3LV8B56+1w3AOgk+EXe/Ub6uiLB6XdH/G/mYjeBCcFwnt3zQqWt4t4NjjnhzQ1CGkBhwOCMFAB71U0qsYgRlwBtQ1tiEJAy44OBdQUmFK3aWS06NLT+ukZAQoKCCjsfbDmk6p78RwX3ncWffmIj8U4kh6GpEwh+9rGy23LDU4GBrrm9DsuDYIGMAYIC/EUNQ7Cq1hn+WM2TI8f+jEyCmvjfn1FssuojHx6tDkyZOaCzr8TNpASzDAk8amlRIrEylcSGsYrcGIstIYWhgDDIM2BiGH3ywFkGAC1U9n38bpVqWGdk6r4HMWrZZaG1D5KLn0qYyBEAKnG1otAxLR8L7Z9nfP13CJHQ/ST4vK8sVHe8JsU0U6uO5hlexo8PI7vNDQomwoBRAwpSmtgJAAztS3QLsOsmBQlBtFJMQhlbbPUBBUR7o2hqHVddLbRsfCPQJ+u3TPw8uGl1yklAlHIJZKo3//XEhlLCtifPFyM7xwCI/lZ8IKTTBbS7pPLIggZZsSQ+zXbT4UYSsnet3UMM5HPT5LGbrDGYQroClyT2Jwnyj9aN949e8mDCwuRFoqKxRHUJ21BSDRELuQYGhvbMVV32Dp2RuxcfHSRBfAYTsbU9nJdFj5EiLkglHkRInC1xoxKbH9hQJIaTDvxxTCUddWl4wg0dCCtqSPDmoVx4Eitpxh64ZtsT6b5ie6pPRkfF90TllxOzEwmipMKRRgHODGgCuJkqIcvDdC2BZ5Y+tlHHMzkAKghbAxcQqQDiKrFBxhqg5MHTivS1tQ+sdsvaQl5Yd6yfdRXNQLsQwXnq/AQFLXEIIjzBSuNaaR0SuEtkQKl9IKjAsbJaWfzo1USDsM6zceDJfeVGgnhhN2N7YOyo5kJz1pa2AbgfrO1gRwXW6vSRQNtddR+EhvKGmseskgTtY2Q7kucYWWgToPHzyUyXry0iXfnBtfl5f/PaWPvPNW/zkOAQegJHltFE5dSaCskHqPVEnqpMAMEgkPtR1pKxyh/N0/vTToubtH1G3RmLjhM8ubKXfWB2mRa9ySOaWS2uT8lTZ0cI6I52Ngv7zAbW9mQVm1cpytu441P38XeXTlQu+e46nyh+bjLkMZRU0MCYTCJWZSG1y7cBWNURpxBlxqFBfEwGnGGhaYPSNwhpSv4DK+/vPynBk9MqRIiOWs8a2WJTm9a+cgh6SaMIMz9W1WjYHHMtv0wSmZdWB9gdsya/rcYVg7JoffCdqlD6ceTpiY59tM0PhJp5WNvra+BQkejCMyBarr8KKYDcZi8sDaCDKYFIGRk+FnSVXzyTO9JxBwF8DLc1dlLn65ooNEYN0fBsu21fTvL6PXnhxXlnLIqqhYYBian4lQ2Lk9ogiALsimiLC1QYfhlV1Hnxh7JfcMqxrpd7U2GFa5t9nOd7Kr+kg4uWvnCpromlJeXlq3Os3ZLOlrZBmNQf1ybVqpxhbA7mRIOCy1+esDOWhIyDv/+3Q7LRbsqH+rKRJ+nba+/+WW7II1s9vvVBuNr7KNF1WUM1bSt5f1Vq01jUVkKfnx8uoti3Or5rbd9782M61azJz/rFywYU/OyKqK1p5G2MS1Z18tGFDwTkvIxcK9RwaMP3a9/tbc62lPj/Nw5B9ey9Ehy/MY4oEqelgNleuyCgdXJlmc3fO5Ll56r5f+n/f+AWFf9jvBgaHpAAAAAElFTkSuQmCC);
    animation: spin 2s linear 1s infinite
}

.mk-cute-theme h1 {
    position: relative;
    font-size: 30px;
    padding: 12px 38px;
    margin: 30px 0
}

.mk-cute-theme h1:before {
    width: 30px;
    height: 30px;
    background-size: 30px 30px
}

.mk-cute-theme h2 {
    position: relative;
    font-size: 24px;
    padding: 12px 36px;
    margin: 28px 0
}

.mk-cute-theme h2:before {
    width: 28px;
    height: 28px;
    background-size: 28px 28px
}

.mk-cute-theme h3 {
    position: relative;
    font-size: 18px;
    padding: 4px 32px;
    margin: 26px 0
}

.mk-cute-theme h3:before {
    width: 24px;
    height: 24px;
    background-size: 24px 24px
}

.mk-cute-theme h4 {
    position: relative;
    padding: 4px 28px;
    font-size: 16px;
    margin: 22px 0
}

.mk-cute-theme h4:before {
    width: 20px;
    height: 20px;
    background-size: 20px 20px
}

.mk-cute-theme h5 {
    position: relative;
    padding: 4px 26px;
    font-size: 15px;
    margin: 20px 0
}

.mk-cute-theme h5:before {
    width: 18px;
    height: 18px;
    background-size: 18px 18px
}

.mk-cute-theme h6 {
    position: relative;
    padding: 4px 22px;
    font-size: 14px;
    margin: 16px 0
}

.mk-cute-theme h6:before {
    width: 16px;
    height: 16px;
    background-size: 16px 16px
}

@media (max-width: 720px) {
    .mk-cute-theme h1 {
        font-size: 24px
    }

    .mk-cute-theme h2 {
        font-size: 20px
    }

    .mk-cute-theme h3 {
        font-size: 18px
    }
}

.mk-cute-theme img {
    max-width: 100%
}

.mk-cute-theme a {
    display: inline-block;
    text-decoration: none;
    color: #409eff;
    border-bottom: 1px solid #409eff
}

.mk-cute-theme a:hover,
.mk-cute-theme a:active {
    color: #007bff;
    border-bottom: 1px solid #007bff
}

.mk-cute-theme ol,
.mk-cute-theme ul {
    padding-left: 28px
}

.mk-cute-theme ol li,
.mk-cute-theme ul li {
    margin-bottom: 0;
    list-style: inherit
}

.mk-cute-theme ol li .task-list-item,
.mk-cute-theme ul li .task-list-item {
    list-style: none
}

.mk-cute-theme ol li .task-list-item ul,
.mk-cute-theme ol li .task-list-item ol,
.mk-cute-theme ul li .task-list-item ul,
.mk-cute-theme ul li .task-list-item ol {
    margin-top: 0
}

.mk-cute-theme ol ul,
.mk-cute-theme ol ol,
.mk-cute-theme ul ul,
.mk-cute-theme ul ol {
    margin-top: 3px
}

.mk-cute-theme ol li {
    padding-left: 6px
}

.mk-cute-theme p {
    line-height: inherit;
    margin-top: 22px;
    margin-bottom: 22px
}

.mk-cute-theme p:empty {
    display: none
}

.md-editor div.mk-cute-theme {
    --md-theme-quote-color: #fff;
    --md-theme-quote-border-color: #409eff;
    --md-theme-quote-bg-color: rgba(54, 172, 225, .75)
}

.md-editor-dark div.mk-cute-theme {
    --md-theme-quote-color: inherit;
    --md-theme-quote-border-color: #265d97;
    --md-theme-quote-bg-color: rgba(18, 80, 108, .75)
}

.mk-cute-theme blockquote {
    position: relative;
    padding: 8px 26px;
    background-color: var(--md-theme-quote-bg-color);
    margin: 16px 0;
    border-left: 4px solid var(--md-theme-quote-border-color);
    border-radius: 5px
}

.mk-cute-theme blockquote:before {
    content: "❝";
    top: 10px;
    left: 8px;
    color: #409eff;
    font-size: 20px;
    line-height: 1;
    font-weight: 700;
    position: absolute;
    opacity: .7
}

.mk-cute-theme blockquote:after {
    content: "❞";
    font-size: 20px;
    position: absolute;
    right: 8px;
    bottom: 0;
    color: #409eff;
    opacity: .7
}

.mk-cute-theme blockquote>p,
.mk-cute-theme blockquote ul li,
.mk-cute-theme blockquote ol li {
    color: var(--md-theme-quote-color)
}

.md-editor div.mk-cute-theme {
    --md-theme-table-color: #000;
    --md-theme-table-border-color: #f6f6f6;
    --md-theme-table-thead-bg-color: #f6f6f6;
    --md-theme-table-stripe-color: #fcfcfc
}

.md-editor-dark div.mk-cute-theme {
    --md-theme-table-color: inherit;
    --md-theme-table-border-color: #1c1c1c;
    --md-theme-table-thead-bg-color: rgba(28, 28, 28, .631372549);
    --md-theme-table-stripe-color: rgba(28, 28, 28, .631372549)
}

.mk-cute-theme table {
    display: inline-block;
    width: auto;
    max-width: 100%;
    overflow: auto;
    border: solid 1px var(--md-theme-table-border-color)
}

.mk-cute-theme thead {
    background-color: var(--md-theme-table-thead-bg-color);
    color: var(--md-theme-table-color);
    text-align: left
}

.mk-cute-theme tr:nth-child(2n) {
    background-color: var(--md-theme-table-stripe-color)
}

.mk-cute-theme th,
.mk-cute-theme td {
    padding: 12px 7px;
    line-height: 24px
}

.mk-cute-theme td {
    min-width: 120px
}

.mk-cute-theme blockquote table tr {
    background-color: var(--md-theme-table-stripe-color)
}

@keyframes spin {
    0% {
        transform: rotate(0)
    }

    to {
        transform: rotate(360deg)
    }
}

.md-editor .mk-cute-theme {
    background-image: linear-gradient(90deg, rgba(50, 58, 66, .25) 3%, rgba(0, 0, 0, 0) 3%), linear-gradient(360deg, rgba(50, 58, 66, .25) 3%, rgba(0, 0, 0, 0) 3%)
}

.md-editor-dark .mk-cute-theme {
    background-image: linear-gradient(90deg, rgba(217, 234, 251, .25) 3%, rgba(0, 0, 0, 0) 3%), linear-gradient(360deg, rgba(217, 234, 251, .25) 3%, rgba(0, 0, 0, 0) 3%);
    --md-theme-bg-color-scrollbar-thumb: #4d4d4d
}

.mk-cute-theme {
    color: var(--md-theme-color);
    word-break: break-word;
    line-height: 1.75;
    font-weight: 400;
    overflow-x: hidden;
    color: #36ace1;
    background-size: 20px 20px;
    background-position: center center
}

.mk-cute-theme ::-webkit-scrollbar {
    width: 6px;
    height: 6px
}

.mk-cute-theme ::-webkit-scrollbar-corner,
.mk-cute-theme ::-webkit-scrollbar-track {
    background-color: var(--md-theme-bg-color-scrollbar-track);
    border-radius: 2px
}

.mk-cute-theme ::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: var(--md-theme-bg-color-scrollbar-thumb)
}

.mk-cute-theme ::-webkit-scrollbar-button:vertical {
    display: none
}

.mk-cute-theme ::-webkit-scrollbar-thumb:vertical:hover {
    background-color: var(--md-theme-bg-color-scrollbar-thumb-hover)
}

.mk-cute-theme ::-webkit-scrollbar-thumb:vertical:active {
    background-color: var(--md-theme-bg-color-scrollbar-thumb-active)
}

.mk-cute-theme h1,
.mk-cute-theme h2,
.mk-cute-theme h3,
.mk-cute-theme h4,
.mk-cute-theme h5,
.mk-cute-theme h6 {
    position: relative;
    word-break: break-all
}

.mk-cute-theme h1 a,
.mk-cute-theme h2 a,
.mk-cute-theme h3 a,
.mk-cute-theme h4 a,
.mk-cute-theme h5 a,
.mk-cute-theme h6 a,
.mk-cute-theme h1 a:hover,
.mk-cute-theme h2 a:hover,
.mk-cute-theme h3 a:hover,
.mk-cute-theme h4 a:hover,
.mk-cute-theme h5 a:hover,
.mk-cute-theme h6 a:hover {
    color: inherit
}

.mk-cute-theme ol>li {
    list-style: decimal
}

.mk-cute-theme ul>li {
    list-style: inherit
}

.mk-cute-theme ol .task-list-item,
.mk-cute-theme ul .task-list-item {
    list-style-type: none
}

.mk-cute-theme ol .task-list-item input,
.mk-cute-theme ul .task-list-item input {
    margin-left: -1.5em;
    margin-right: .1em
}

.mk-cute-theme a {
    text-decoration: none
}

.mk-cute-theme pre,
.mk-cute-theme code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;
    font-size: 14px
}

.mk-cute-theme pre {
    margin: 20px 0
}

.mk-cute-theme pre code {
    display: block;
    line-height: 1;
    overflow: auto
}

.mk-cute-theme pre code .code-block {
    display: inline-block;
    width: 100%;
    overflow: auto;
    vertical-align: bottom
}

.mk-cute-theme pre code[language]:before {
    content: attr(language);
    font-size: 12px;
    position: absolute;
    top: 11px;
    right: 40px;
    line-height: 1;
    z-index: 1
}

.mk-cute-theme .copy-button {
    color: var(--md-theme-code-block-color);
    position: absolute;
    font-size: 12px;
    top: 5px;
    right: 7px;
    line-height: 1;
    cursor: pointer;
    z-index: 1
}

.mk-cute-theme .copy-button:before {
    content: attr(data-tips);
    color: var(--md-theme-code-copy-tips-color);
    background-color: var(--md-theme-code-copy-tips-bg-color);
    position: absolute;
    font-size: 12px;
    font-family: sans-serif;
    width: max-content;
    text-align: center;
    padding: 4px;
    border-radius: 2px;
    box-shadow: 0 0 2px #0003;
    left: 0;
    top: 50%;
    transform: translate(-100%, -50%)
}

.mk-cute-theme .copy-button:after {
    content: "";
    color: var(--md-theme-code-copy-tips-bg-color);
    position: absolute;
    width: 0;
    height: 0;
    border: 5px solid rgba(0, 0, 0, 0);
    border-right-width: 0;
    border-left-color: currentColor;
    left: -2px;
    top: 50%;
    transform: translateY(-50%);
    filter: drop-shadow(4px 0 2px rgba(0, 0, 0, .2))
}

.mk-cute-theme .copy-button:before,
.mk-cute-theme .copy-button:after {
    visibility: hidden;
    transition: .3s
}

.mk-cute-theme .copy-button:hover:before,
.mk-cute-theme .copy-button:hover:after {
    visibility: visible
}

.mk-cute-theme hr {
    height: 1px;
    margin: 10px 0;
    border: none;
    border-top: 1px solid var(--md-theme-border-color)
}

.mk-cute-theme figure {
    margin: 0 0 1em;
    display: inline-flex;
    flex-direction: column;
    text-align: center
}

.mk-cute-theme figure figcaption {
    color: var(--md-theme-color);
    font-size: .875em;
    margin-top: 5px
}

.mk-cute-theme .md-editor-mermaid {
    line-height: 1
}

.mk-cute-theme hr {
    position: relative;
    width: 98%;
    height: 1px;
    border: none;
    margin-top: 32px;
    margin-bottom: 32px;
    background-image: linear-gradient(to right, #36ace1, #dff0fe, #36ace1);
    overflow: visible
}

.mk-cute-theme del {
    color: #36ace1
}

.md-editor div.smart-blue-theme {
    --md-theme-code-inline-color: #d63200;
    --md-theme-code-inline-bg-color: #fff5f5;
    --md-theme-code-block-color: #333;
    --md-theme-code-block-bg-color: #f8f8f8
}

.md-editor-dark div.smart-blue-theme {
    --md-theme-code-inline-color: #e06c75;
    --md-theme-code-inline-bg-color: #1a1a1a;
    --md-theme-code-block-color: #999;
    --md-theme-code-block-bg-color: #1a1a1a
}

.smart-blue-theme pre {
    position: relative
}

.smart-blue-theme pre code {
    padding: 22px 12px;
    margin: 0;
    word-break: normal;
    display: block;
    overflow-x: auto;
    color: var(--md-theme-code-block-color);
    background-color: var(--md-theme-code-block-bg-color)
}

.smart-blue-theme pre code>* {
    line-height: 1.6
}

.smart-blue-theme pre code span[rn-wrapper] {
    top: 22px
}

.smart-blue-theme code {
    border-radius: 2px;
    overflow-x: auto;
    background-color: var(--md-theme-code-inline-bg-color);
    color: #ff502c;
    padding: .065em .4em
}

.smart-blue-theme pre,
.smart-blue-theme code {
    line-height: 1.75;
    font-family: Menlo, Monaco, Consolas, Courier New, monospace
}

.md-editor div.smart-blue-theme {
    --md-theme-heading-color: #135ce0
}

.md-editor-dark div.smart-blue-theme {
    --md-theme-heading-color: #3a73dd
}

.smart-blue-theme h1,
.smart-blue-theme h2,
.smart-blue-theme h3,
.smart-blue-theme h4,
.smart-blue-theme h5,
.smart-blue-theme h6 {
    padding: 30px 0;
    margin: 0;
    color: var(--md-theme-heading-color)
}

.smart-blue-theme h1 a,
.smart-blue-theme h2 a,
.smart-blue-theme h3 a,
.smart-blue-theme h4 a,
.smart-blue-theme h5 a,
.smart-blue-theme h6 a {
    border: none
}

.smart-blue-theme h1 {
    position: relative;
    text-align: center;
    font-size: 22px;
    margin: 50px 0
}

.smart-blue-theme h2 {
    position: relative;
    font-size: 20px;
    border-left: 4px solid;
    padding: 0 0 0 10px;
    margin: 30px 0
}

.smart-blue-theme h3 {
    font-size: 16px
}

.smart-blue-theme img {
    max-width: 100%;
    margin: 0 auto;
    display: block
}

.md-editor div.smart-blue-theme {
    --md-theme-link-color: #036aca
}

.md-editor-dark div.smart-blue-theme {
    --md-theme-link-color: #2d7dc7
}

.smart-blue-theme a {
    color: var(--md-theme-link-color);
    font-weight: 400;
    text-decoration: none
}

.smart-blue-theme ul,
.smart-blue-theme ol {
    margin-top: 1em;
    padding-left: 1.6em
}

.smart-blue-theme li {
    line-height: 2;
    margin-bottom: 0;
    list-style: inherit
}

.smart-blue-theme p {
    line-height: 2;
    font-weight: 400
}

.smart-blue-theme *+p {
    margin-top: 16px
}

.md-editor div.smart-blue-theme {
    --md-theme-quote-color: #666;
    --md-theme-quote-bg-color: #fff9f9;
    --md-theme-quote-border-color: #b2aec5
}

.md-editor-dark div.smart-blue-theme {
    --md-theme-quote-color: #999;
    --md-theme-quote-bg-color: #2a2a2a;
    --md-theme-quote-border-color: #0063bb
}

.smart-blue-theme blockquote {
    background-color: var(--md-theme-quote-bg-color);
    margin: 2em 0;
    padding: 2px 20px;
    border-left: 4px solid var(--md-theme-quote-border-color)
}

.smart-blue-theme blockquote p {
    color: var(--md-theme-quote-color);
    line-height: 2
}

.md-editor div.smart-blue-theme {
    --md-theme-table-border-color: #dfe2e5;
    --md-theme-table-bg-color: #f6f8fa
}

.md-editor-dark div.smart-blue-theme {
    --md-theme-table-border-color: #2d2d2d;
    --md-theme-table-bg-color: #0c0c0c
}

.smart-blue-theme table {
    border-collapse: collapse;
    margin: 1rem 0;
    overflow-x: auto
}

.smart-blue-theme table tr {
    border-top: 1px solid var(--md-theme-table-border-color)
}

.smart-blue-theme table tr:nth-child(2n) {
    background-color: var(--md-theme-table-bg-color)
}

.smart-blue-theme table tr th,
.smart-blue-theme table tr td {
    border: 1px solid var(--md-theme-table-border-color);
    padding: .6em 1em
}

.smart-blue-theme blockquote table {
    line-height: initial
}

.smart-blue-theme blockquote table tr th,
.smart-blue-theme blockquote table tr td {
    border-color: var(--md-theme-border-color-inset)
}

.smart-blue-theme blockquote table tbody tr:nth-child(n) {
    background-color: inherit
}

.md-editor .smart-blue-theme {
    --md-theme-color: #595959
}

.md-editor .smart-blue-theme {
    background-image: linear-gradient(90deg, rgba(60, 10, 30, .04) 3%, rgba(0, 0, 0, 0) 3%), linear-gradient(360deg, rgba(60, 10, 30, .04) 3%, rgba(0, 0, 0, 0) 3%)
}

.md-editor-dark .smart-blue-theme {
    --md-theme-color: #999
}

.md-editor-dark .smart-blue-theme {
    background-image: linear-gradient(90deg, rgba(207, 207, 207, .04) 3%, rgba(255, 255, 255, 0) 3%), linear-gradient(360deg, rgba(207, 207, 207, .04) 3%, rgba(255, 255, 255, 0) 3%)
}

.smart-blue-theme {
    color: var(--md-theme-color);
    font-family: -apple-system, system-ui, BlinkMacSystemFont, Helvetica Neue, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
    background-size: 20px 20px;
    background-position: center center
}

.smart-blue-theme ::-webkit-scrollbar {
    width: 6px;
    height: 6px
}

.smart-blue-theme ::-webkit-scrollbar-corner,
.smart-blue-theme ::-webkit-scrollbar-track {
    background-color: var(--md-theme-bg-color-scrollbar-track);
    border-radius: 2px
}

.smart-blue-theme ::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: var(--md-theme-bg-color-scrollbar-thumb)
}

.smart-blue-theme ::-webkit-scrollbar-button:vertical {
    display: none
}

.smart-blue-theme ::-webkit-scrollbar-thumb:vertical:hover {
    background-color: var(--md-theme-bg-color-scrollbar-thumb-hover)
}

.smart-blue-theme ::-webkit-scrollbar-thumb:vertical:active {
    background-color: var(--md-theme-bg-color-scrollbar-thumb-active)
}

.smart-blue-theme h1,
.smart-blue-theme h2,
.smart-blue-theme h3,
.smart-blue-theme h4,
.smart-blue-theme h5,
.smart-blue-theme h6 {
    position: relative;
    word-break: break-all
}

.smart-blue-theme h1 a,
.smart-blue-theme h2 a,
.smart-blue-theme h3 a,
.smart-blue-theme h4 a,
.smart-blue-theme h5 a,
.smart-blue-theme h6 a,
.smart-blue-theme h1 a:hover,
.smart-blue-theme h2 a:hover,
.smart-blue-theme h3 a:hover,
.smart-blue-theme h4 a:hover,
.smart-blue-theme h5 a:hover,
.smart-blue-theme h6 a:hover {
    color: inherit
}

.smart-blue-theme ol>li {
    list-style: decimal
}

.smart-blue-theme ul>li {
    list-style: inherit
}

.smart-blue-theme ol .task-list-item,
.smart-blue-theme ul .task-list-item {
    list-style-type: none
}

.smart-blue-theme ol .task-list-item input,
.smart-blue-theme ul .task-list-item input {
    margin-left: -1.5em;
    margin-right: .1em
}

.smart-blue-theme a {
    text-decoration: none
}

.smart-blue-theme pre,
.smart-blue-theme code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;
    font-size: 14px
}

.smart-blue-theme pre {
    margin: 20px 0
}

.smart-blue-theme pre code {
    display: block;
    line-height: 1;
    overflow: auto
}

.smart-blue-theme pre code .code-block {
    display: inline-block;
    width: 100%;
    overflow: auto;
    vertical-align: bottom
}

.smart-blue-theme pre code[language]:before {
    content: attr(language);
    font-size: 12px;
    position: absolute;
    top: 11px;
    right: 40px;
    line-height: 1;
    z-index: 1
}

.smart-blue-theme .copy-button {
    color: var(--md-theme-code-block-color);
    position: absolute;
    font-size: 12px;
    top: 5px;
    right: 7px;
    line-height: 1;
    cursor: pointer;
    z-index: 1
}

.smart-blue-theme .copy-button:before {
    content: attr(data-tips);
    color: var(--md-theme-code-copy-tips-color);
    background-color: var(--md-theme-code-copy-tips-bg-color);
    position: absolute;
    font-size: 12px;
    font-family: sans-serif;
    width: max-content;
    text-align: center;
    padding: 4px;
    border-radius: 2px;
    box-shadow: 0 0 2px #0003;
    left: 0;
    top: 50%;
    transform: translate(-100%, -50%)
}

.smart-blue-theme .copy-button:after {
    content: "";
    color: var(--md-theme-code-copy-tips-bg-color);
    position: absolute;
    width: 0;
    height: 0;
    border: 5px solid rgba(0, 0, 0, 0);
    border-right-width: 0;
    border-left-color: currentColor;
    left: -2px;
    top: 50%;
    transform: translateY(-50%);
    filter: drop-shadow(4px 0 2px rgba(0, 0, 0, .2))
}

.smart-blue-theme .copy-button:before,
.smart-blue-theme .copy-button:after {
    visibility: hidden;
    transition: .3s
}

.smart-blue-theme .copy-button:hover:before,
.smart-blue-theme .copy-button:hover:after {
    visibility: visible
}

.smart-blue-theme hr {
    height: 1px;
    margin: 10px 0;
    border: none;
    border-top: 1px solid var(--md-theme-border-color)
}

.smart-blue-theme figure {
    margin: 0 0 1em;
    display: inline-flex;
    flex-direction: column;
    text-align: center
}

.smart-blue-theme figure figcaption {
    color: var(--md-theme-color);
    font-size: .875em;
    margin-top: 5px
}

.smart-blue-theme .md-editor-mermaid {
    line-height: 1
}

.smart-blue-theme strong,
.smart-blue-theme em strong {
    color: #036aca
}

.smart-blue-theme hr {
    border-top: 1px solid #135ce0
}

.md-editor-catalog-editor {
    --md-color: #3f4a54;
    --md-hover-color: #000;
    --md-bk-color: #fff;
    --md-bk-color-outstand: #f6f6f6;
    --md-bk-hover-color: #f5f7fa;
    --md-border-color: #e6e6e6;
    --md-border-hover-color: #b9b9b9;
    --md-border-active-color: #999;
    --md-modal-mask: #00000073;
    --md-scrollbar-bg-color: #e2e2e2;
    --md-scrollbar-thumb-color: #0000004d;
    --md-scrollbar-thumb-hover-color: #00000059;
    --md-scrollbar-thumb-active-color: #00000061;
    position: absolute;
    overflow: auto;
    top: 0;
    right: 0;
    height: 100%;
    background-color: var(--md-bk-color-outstand);
    border-left: 1px solid var(--md-border-color);
    width: 200px;
    box-sizing: border-box;
    margin: 0;
    padding: 5px 10px;
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum";
    z-index: 2
}

.md-editor-catalog>.md-editor-catalog-link {
    padding: 5px 8px
}

.md-editor-catalog-link {
    padding: 5px 0 5px 1em;
    line-height: 1.2
}

.md-editor-catalog-link span {
    display: inline-block;
    width: 100%;
    position: relative;
    overflow: hidden;
    color: var(--md-color);
    white-space: nowrap;
    text-overflow: ellipsis;
    transition: color .3s;
    cursor: pointer
}

.md-editor-catalog-link span:hover {
    color: #73d13d
}

.md-editor-catalog-wrapper>.md-editor-catalog-link {
    padding-top: 5px;
    padding-bottom: 5px
}

.md-editor-catalog-wrapper>.md-editor-catalog-link:first-of-type {
    padding-top: 10px
}

.md-editor-catalog-wrapper>.md-editor-catalog-link:last-of-type {
    padding-bottom: 0
}

.md-editor-catalog-active>span {
    color: #73d13d
}

.md-editor-catalog-dark {
    --md-color: #999;
    --md-hover-color: #bbb;
    --md-bk-color: #000;
    --md-bk-color-outstand: #111;
    --md-bk-hover-color: #1b1a1a;
    --md-border-color: #2d2d2d;
    --md-border-hover-color: #636262;
    --md-border-active-color: #777;
    --md-modal-mask: #00000073;
    --md-scrollbar-bg-color: #0f0f0f;
    --md-scrollbar-thumb-color: #2d2d2d;
    --md-scrollbar-thumb-hover-color: #3a3a3a;
    --md-scrollbar-thumb-active-color: #3a3a3a
}

.md-editor {
    --md-color: #3f4a54;
    --md-hover-color: #000;
    --md-bk-color: #fff;
    --md-bk-color-outstand: #f6f6f6;
    --md-bk-hover-color: #f5f7fa;
    --md-border-color: #e6e6e6;
    --md-border-hover-color: #b9b9b9;
    --md-border-active-color: #999;
    --md-modal-mask: #00000073;
    --md-scrollbar-bg-color: #e2e2e2;
    --md-scrollbar-thumb-color: #0000004d;
    --md-scrollbar-thumb-hover-color: #00000059;
    --md-scrollbar-thumb-active-color: #00000061;
    width: 100%;
    height: 500px;
    position: relative;
    box-sizing: border-box;
    border: 1px solid var(--md-border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    color: var(--md-color);
    background-color: var(--md-bk-color);
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI Variable, Segoe UI, system-ui, ui-sans-serif, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji"
}

.md-editor ::-webkit-scrollbar {
    width: 6px;
    height: 6px
}

.md-editor ::-webkit-scrollbar-corner,
.md-editor ::-webkit-scrollbar-track {
    background-color: var(--md-scrollbar-bg-color)
}

.md-editor ::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: var(--md-scrollbar-thumb-color)
}

.md-editor ::-webkit-scrollbar-button:vertical {
    display: none
}

.md-editor ::-webkit-scrollbar-thumb:vertical:hover {
    background-color: var(--md-scrollbar-thumb-hover-color)
}

.md-editor ::-webkit-scrollbar-thumb:vertical:active {
    background-color: var(--md-scrollbar-thumb-active-color)
}

.md-editor .md-editor-fullscreen {
    position: fixed !important;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: auto !important;
    height: auto !important;
    z-index: 10000
}

.md-editor-icon {
    width: 24px;
    height: 24px;
    fill: currentColor;
    overflow: hidden;
    display: block
}

.md-editor .md-editor-iconfont {
    font-size: 24px;
    width: 24px;
    height: 24px;
    display: block
}

.md-editor-preview-wrapper {
    position: relative;
    flex: 1;
    box-sizing: border-box;
    overflow: auto;
    padding: 10px 20px
}

.md-editor-preview {
    font-size: 16px;
    word-break: break-all;
    overflow: hidden
}

.md-editor [data-show=false] {
    display: none
}

.md-editor-previewOnly {
    border: none;
    height: auto
}

.md-editor-previewOnly .md-editor-content {
    height: 100%
}

.md-editor-previewOnly .md-editor-preview {
    padding: 0
}

.md-editor-dark {
    --md-color: #999;
    --md-hover-color: #bbb;
    --md-bk-color: #000;
    --md-bk-color-outstand: #111;
    --md-bk-hover-color: #1b1a1a;
    --md-border-color: #2d2d2d;
    --md-border-hover-color: #636262;
    --md-border-active-color: #777;
    --md-modal-mask: #00000073;
    --md-scrollbar-bg-color: #0f0f0f;
    --md-scrollbar-thumb-color: #2d2d2d;
    --md-scrollbar-thumb-hover-color: #3a3a3a;
    --md-scrollbar-thumb-active-color: #3a3a3a
}

.medium-zoom-overlay,
.medium-zoom-image--opened {
    z-index: 100001
}