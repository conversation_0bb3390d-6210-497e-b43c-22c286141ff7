<script setup>
import { onMounted, ref, onBeforeUnmount } from "vue";
import { useUserStore } from "@/stores/user";
const router = useRouter();
const userStore = useUserStore();
import _ from "lodash";
import moment from "moment";

const listItems = ref([]);
let tableDisplay = ref(true);
let toggleSideBarMenu = ref(false);
let toggleConfirmDelete = ref(false);

const endpoint = ref(userStore.mainDevServer);
let selectedID = ref(null);
const statusFilter = ref("all");

const showImageModal = ref(false);
const currentModalImage = ref("");

const showPersonalInfoModal = ref(false);
const currentPersonalInfo = ref(null);

const showLogsModal = ref(false);
const showPaymentModal = ref(false);

const showPrepDocModal = ref(false);
const showReleasingDocModal = ref(false);
const showTransactionClosedModal = ref(false);

const currentItem = ref(null);
const newLogRemarks = ref("");

const verifySuccessMessage = ref("");
const verifyErrorMessage = ref("");
const isVerifying = ref(false);
const isConfirming = ref(false);

const showDuplicateAlertModal = ref(false);

const openImageModal = (imageUrl) => {
  currentModalImage.value = imageUrl;
  showImageModal.value = true;
  isModalOpen.value = true;
  // Prevent scrolling on the body when modal is open
  document.body.style.overflow = "hidden";
};

const closeImageModal = () => {
  showImageModal.value = false;
  isModalOpen.value = checkIfModalOpen();
  // Restore scrolling
  document.body.style.overflow = "auto";
};

const verifiedConfirm = async (item) => {
  verifySuccessMessage.value = "";
  verifyErrorMessage.value = "";
  isVerifying.value = true; // Start loading

  const newStatus = "Reviewed and Verified";
  const latestLog = item.logs?.[0];

  if (
    latestLog &&
    latestLog.status_remarks.toLowerCase().trim() === newStatus.toLowerCase()
  ) {
    // showDuplicateAlertModal.value = true;
    isVerifying.value = false; // Stop loading
    return;
  }

  try {
    const newLog = {
      timestamp: moment().format("MMMM DD, YYYY h:mm:ss A"),
      status_remarks: newStatus,
    };

    const updatedLogs = item.logs ? [...item.logs] : [];
    updatedLogs.unshift(newLog);

    selectedID.value = item.id;
    const updatedItem = { ...item, logs: updatedLogs };

    await $fetch(`${endpoint.value}/api/registrar/${selectedID.value}/edit/`, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: updatedItem,
    });

    // Update the item locally for immediate UI feedback
    item.logs = updatedLogs;

    // Also update the item in the main list to ensure reactivity
    const itemIndex = listItems.value.findIndex(
      (listItem) => listItem.id === item.id
    );
    if (itemIndex !== -1) {
      listItems.value[itemIndex].logs = updatedLogs;
    }

    // Update the status immediately for this specific item
    verificationStatuses.value[item.id] = true;

    await $fetch(`${endpoint.value}/api/registrar/status/update/`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: {
        id: item.id,
        latest_status: newStatus,
      },
    });

    await fetchListItemsQuietly();

    // Set success message
    verifySuccessMessage.value = "✅ Confirm and email sent successfully!";

    // Clear success message after 5 seconds but keep modal open
    setTimeout(() => {
      verifySuccessMessage.value = "";
    }, 3000);
  } catch (error) {
    console.error("Error verifying and sending email:", error);
    verifyErrorMessage.value = "❌ Failed to verify and send email.";

    // Clear error message after 5 seconds but keep modal open
    setTimeout(() => {
      verifyErrorMessage.value = "";
    }, 3000);
  } finally {
    isVerifying.value = false; // Stop loading regardless of outcome
  }
};

const confirmPrepDoc = async (item) => {
  verifySuccessMessage.value = "";
  verifyErrorMessage.value = "";
  isConfirming.value = true; // Start loading

  const newStatus = "Preparing Documents";
  const latestLog = item.logs?.[0];

  // if (
  //   latestLog &&
  //   latestLog.status_remarks.toLowerCase().trim() === newStatus.toLowerCase()
  // ) {
  //   // showDuplicateAlertModal.value = true;
  //   isConfirming.value = false; // Stop loading
  //   return;
  // }

  try {
    const newLog = {
      timestamp: moment().format("MMMM DD, YYYY h:mm:ss A"),
      status_remarks: newStatus,
    };

    const updatedLogs = item.logs ? [...item.logs] : [];
    updatedLogs.unshift(newLog);

    selectedID.value = item.id;
    const updatedItem = { ...item, logs: updatedLogs };

    await $fetch(`${endpoint.value}/api/registrar/${selectedID.value}/edit/`, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: updatedItem,
    });

    // Update the item locally for immediate UI feedback
    item.logs = updatedLogs;

    // Also update the item in the main list to ensure reactivity
    const itemIndex = listItems.value.findIndex(
      (listItem) => listItem.id === item.id
    );
    if (itemIndex !== -1) {
      listItems.value[itemIndex].logs = updatedLogs;
    }

    // Update the status immediately for this specific item
    prepDocStatuses.value[item.id] = true;

    await $fetch(`${endpoint.value}/api/registrar/status/update/`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: {
        id: item.id,
        latest_status: newStatus,
      },
    });

    await fetchListItemsQuietly();

    // Set success message
    verifySuccessMessage.value = "✅ Confirm and email sent successfully!";

    // Clear success message after 5 seconds but keep modal open
    setTimeout(() => {
      verifySuccessMessage.value = "";
    }, 3000);
  } catch (error) {
    console.error("Error verifying and sending email:", error);
    verifyErrorMessage.value = "❌ Failed to verify and send email.";

    // Clear error message after 5 seconds but keep modal open
    setTimeout(() => {
      verifyErrorMessage.value = "";
    }, 3000);
  } finally {
    isConfirming.value = false; // Stop loading regardless of outcome
    showPrepDocModal.value = false;
  }
};

const confirmReleasingDoc = async (item) => {
  verifySuccessMessage.value = "";
  verifyErrorMessage.value = "";
  isConfirming.value = true; // Start loading

  const newStatus = "Releasing Documents";
  const latestLog = item.logs?.[0];

  // if (
  //   latestLog &&
  //   latestLog.status_remarks.toLowerCase().trim() === newStatus.toLowerCase()
  // ) {
  //   // showDuplicateAlertModal.value = true;
  //   isConfirming.value = false; // Stop loading
  //   return;
  // }

  try {
    const newLog = {
      timestamp: moment().format("MMMM DD, YYYY h:mm:ss A"),
      status_remarks: newStatus,
    };

    const updatedLogs = item.logs ? [...item.logs] : [];
    updatedLogs.unshift(newLog);

    selectedID.value = item.id;
    const updatedItem = { ...item, logs: updatedLogs };

    await $fetch(`${endpoint.value}/api/registrar/${selectedID.value}/edit/`, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: updatedItem,
    });

    // Update the item locally for immediate UI feedback
    item.logs = updatedLogs;

    // Also update the item in the main list to ensure reactivity
    const itemIndex = listItems.value.findIndex(
      (listItem) => listItem.id === item.id
    );
    if (itemIndex !== -1) {
      listItems.value[itemIndex].logs = updatedLogs;
    }

    // Update the status immediately for this specific item
    releasingDocStatuses.value[item.id] = true;

    await $fetch(`${endpoint.value}/api/registrar/status/update/`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: {
        id: item.id,
        latest_status: newStatus,
      },
    });

    await fetchListItemsQuietly();

    // Set success message
    verifySuccessMessage.value = "✅ Confirm and email sent successfully!";

    // Clear success message after 5 seconds but keep modal open
    setTimeout(() => {
      verifySuccessMessage.value = "";
    }, 3000);
  } catch (error) {
    console.error("Error verifying and sending email:", error);
    verifyErrorMessage.value = "❌ Failed to verify and send email.";

    // Clear error message after 5 seconds but keep modal open
    setTimeout(() => {
      verifyErrorMessage.value = "";
    }, 3000);
  } finally {
    isConfirming.value = false; // Stop loading regardless of outcome
    showReleasingDocModal.value = false;
  }
};

const confirmTransactionClosed = async (item) => {
  verifySuccessMessage.value = "";
  verifyErrorMessage.value = "";
  isConfirming.value = true; // Start loading

  const newStatus = "Transaction Closed";
  const latestLog = item.logs?.[0];

  // if (
  //   latestLog &&
  //   latestLog.status_remarks.toLowerCase().trim() === newStatus.toLowerCase()
  // ) {
  //   // showDuplicateAlertModal.value = true;
  //   isConfirming.value = false; // Stop loading
  //   return;
  // }

  try {
    const newLog = {
      timestamp: moment().format("MMMM DD, YYYY h:mm:ss A"),
      status_remarks: newStatus,
    };

    const updatedLogs = item.logs ? [...item.logs] : [];
    updatedLogs.unshift(newLog);

    selectedID.value = item.id;
    const updatedItem = { ...item, logs: updatedLogs };

    await $fetch(`${endpoint.value}/api/registrar/${selectedID.value}/edit/`, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: updatedItem,
    });

    // Update the item locally for immediate UI feedback
    item.logs = updatedLogs;

    // Also update the item in the main list to ensure reactivity
    const itemIndex = listItems.value.findIndex(
      (listItem) => listItem.id === item.id
    );
    if (itemIndex !== -1) {
      listItems.value[itemIndex].logs = updatedLogs;
    }

    // Update the status immediately for this specific item
    transactionClosedStatuses.value[item.id] = true;

    await $fetch(`${endpoint.value}/api/registrar/status/transaction-closed/`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: {
        id: item.id,
        latest_status: newStatus,
      },
    });

    await fetchListItemsQuietly();

    // Set success message
    verifySuccessMessage.value = "✅ Confirm and email sent successfully!";

    // Clear success message after 5 seconds but keep modal open
    setTimeout(() => {
      verifySuccessMessage.value = "";
    }, 3000);
  } catch (error) {
    console.error("Error verifying and sending email:", error);
    verifyErrorMessage.value = "❌ Failed to verify and send email.";

    // Clear error message after 5 seconds but keep modal open
    setTimeout(() => {
      verifyErrorMessage.value = "";
    }, 3000);
  } finally {
    isConfirming.value = false; // Stop loading regardless of outcome
    showTransactionClosedModal.value = false;
  }
};

// Send email
const sendEmailDetailFee = async (item) => {
  isSendingPayment.value = true;

  const payload = {
    ...currentItemInfo.value,
    date_graduated_last_attended: moment(
      currentItemInfo.value.date_graduated_last_attended
    ).format("LL"),
  };

  try {
    await $fetch(endpoint.value + "/api/registrar/payment/fees/", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: payload,
    });

    showSuccessMessage.value = true;

    setTimeout(() => {
      showSuccessMessage.value = false;
    }, 3000);
  } catch (error) {
    console.error("Error sending email:", error);
  } finally {
    isSendingPayment.value = false;

    assessmentConfirm(item);
  }
};

const assessmentConfirm = async (item) => {
  verifySuccessMessage.value = "";
  verifyErrorMessage.value = "";
  isVerifying.value = true; // Start loading

  const newStatus = "Assessment";
  const latestLog = item.logs?.[0];

  // if (
  //   latestLog &&
  //   latestLog.status_remarks.toLowerCase().trim() === newStatus.toLowerCase()
  // ) {
  //   // showDuplicateAlertModal.value = true;
  //   isVerifying.value = false; // Stop loading
  //   return;
  // }

  try {
    const newLog = {
      timestamp: moment().format("MMMM DD, YYYY h:mm:ss A"),
      status_remarks: newStatus,
    };

    const updatedLogs = item.logs ? [...item.logs] : [];
    updatedLogs.unshift(newLog);

    selectedID.value = item.id;
    const updatedItem = { ...item, logs: updatedLogs };

    await $fetch(`${endpoint.value}/api/registrar/${selectedID.value}/edit/`, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: updatedItem,
    });

    // Update the item locally for immediate UI feedback
    item.logs = updatedLogs;

    // Also update the item in the main list to ensure reactivity
    const itemIndex = listItems.value.findIndex(
      (listItem) => listItem.id === item.id
    );
    if (itemIndex !== -1) {
      listItems.value[itemIndex].logs = updatedLogs;
    }

    // Update the status immediately for this specific item
    paymentStatuses.value[item.id] = true;

    await $fetch(`${endpoint.value}/api/registrar/status/update/`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: {
        id: item.id,
        latest_status: newStatus,
      },
    });

    await fetchListItemsQuietly();

    // Set success message
    verifySuccessMessage.value = "✅ Confirm and email sent successfully!";

    // Clear success message after 5 seconds but keep modal open
    setTimeout(() => {
      verifySuccessMessage.value = "";
    }, 3000);
  } catch (error) {
    console.error("Error verifying and sending email:", error);
    verifyErrorMessage.value = "❌ Failed to verify and send email.";

    // Clear error message after 5 seconds but keep modal open
    setTimeout(() => {
      verifyErrorMessage.value = "";
    }, 3000);
  } finally {
    isVerifying.value = false; // Stop loading regardless of outcome
  }
};

// Ensure only one modal is visible at a time
const closeAllModals = () => {
  showPersonalInfoModal.value = false;
  showPaymentModal.value = false;
  showPrepDocModal.value = false;
  showReleasingDocModal.value = false;
  showTransactionClosedModal.value = false;
  showLogsModal.value = false;
  // Keep image/delete states unchanged as they are separate flows
  currentItem.value = null;
  currentPersonalInfo.value = null;
};

const openLogsModal = (item) => {
  closeAllModals();
  currentItem.value = item;
  showLogsModal.value = true;
  isModalOpen.value = true;
  newLogRemarks.value = "";
  document.body.style.overflow = "hidden";
};

const closeLogsModal = () => {
  showLogsModal.value = false;
  isModalOpen.value = checkIfModalOpen();
  // Restore scrolling
  document.body.style.overflow = "auto";
};

const openPaymentModal = (item) => {
  closeAllModals();
  currentItem.value = item;
  showPaymentModal.value = true;
  isModalOpen.value = true;
  document.body.style.overflow = "hidden";

  currentItemInfo.value.fullname = `${item.firstname} ${item.middlename} ${item.lastname}`;
  currentItemInfo.value.email = item.email;
  currentItemInfo.value.course = item.course;
  currentItemInfo.value.tracking_id = item.tracking_id;
  currentItemInfo.value.college = item.college;
  currentItemInfo.value.date_graduated_last_attended = moment(
    item.date_graduated_last_attended
  ).format("YYYY-MM-DD");

  console.log(
    "currentItemInfo.value.date_graduated_last_attended",
    currentItemInfo.value.date_graduated_last_attended
  );

  const types = item.type_document_requests || [];

  // Initialize detail_fees with default amounts based on document type
  currentItemInfo.value.detail_fees = types.map((docName) => {
    // Check if it's a standard document type with a predefined fee
    if (documentFees.value[docName]) {
      return {
        fee_name: docName,
        amount: documentFees.value[docName],
      };
    }
    // For "Other" document types or types without predefined fees
    else {
      return {
        fee_name: docName,
        amount: 0, // Default to 0 for other document types
      };
    }
  });

  // Calculate initial total
  updateTotal();

  // console.log("Mapped Detail Fees:", currentItemInfo.value.detail_fees);
  console.log("tracking id:", currentItemInfo.value.tracking_id);
};

const closePaymentModal = () => {
  showPaymentModal.value = false;
  currentItem.value = null;
  isModalOpen.value = checkIfModalOpen();
  // Restore scrolling
  document.body.style.overflow = "auto";
};

const openPersonalInfoModal = (item) => {
  closeAllModals();
  currentPersonalInfo.value = item;
  showPersonalInfoModal.value = true;
  isModalOpen.value = true;
  // Prevent scrolling on the body when modal is open
  document.body.style.overflow = "hidden";
};

const closePersonalInfoModal = () => {
  showPersonalInfoModal.value = false;
  currentPersonalInfo.value = null;
  isModalOpen.value = checkIfModalOpen();
  // Restore scrolling
  document.body.style.overflow = "auto";
};

const openPrepDocModal = (item) => {
  closeAllModals();
  currentItem.value = item;
  showPrepDocModal.value = true;
  isModalOpen.value = true;
  // Prevent scrolling on the body when modal is open
  document.body.style.overflow = "hidden";
};

const closePrepDocModal = () => {
  showPrepDocModal.value = false;
  currentItem.value = null;
  isModalOpen.value = checkIfModalOpen();
  // Restore scrolling
  document.body.style.overflow = "auto";
};

const openReleasingDocModal = (item) => {
  closeAllModals();
  currentItem.value = item;
  showReleasingDocModal.value = true;
  isModalOpen.value = true;
  // Prevent scrolling on the body when modal is open
  document.body.style.overflow = "hidden";
};

const closeReleasingDocModal = () => {
  showReleasingDocModal.value = false;
  currentItem.value = null;
  isModalOpen.value = checkIfModalOpen();
  // Restore scrolling
  document.body.style.overflow = "auto";
};

const openTransactionClosedModal = (item) => {
  closeAllModals();
  currentItem.value = item;
  showTransactionClosedModal.value = true;
  isModalOpen.value = true;
  // Prevent scrolling on the body when modal is open
  document.body.style.overflow = "hidden";
};

const closeTransactionClosedModal = () => {
  showTransactionClosedModal.value = false;
  currentItem.value = null;
  isModalOpen.value = checkIfModalOpen();
  // Restore scrolling
  document.body.style.overflow = "auto";
};

const checkIfModalOpen = () => {
  return (
    showPaymentModal.value ||
    showPrepDocModal.value ||
    showReleasingDocModal.value ||
    showTransactionClosedModal.value ||
    showLogsModal.value ||
    showImageModal.value ||
    toggleConfirmDelete.value ||
    showPersonalInfoModal.value
  );
};

const selectedCollege = ref("");
const colleges = ref([
  {
    value: "Arts and Sciences, Engineering, Architecture, Computer Studies",
    label: "Arts and Sciences, Engineering, Architecture, Computer Studies",
  },
  {
    value: "Business, Commerce, Accountancy",
    label: "Business, Commerce, Accountancy",
  },
  {
    value: "Graduate School / Nursing",
    label: "Graduate School / Nursing",
  },
  {
    value: "Tourism Management, Hotel Management, Education",
    label: "Tourism Management, Hotel Management, Education",
  },
  {
    value: "Criminology / BS Psychology",
    label: "Criminology / BS Psychology",
  },
]);

const sortColumn = ref(null);
const sortDirection = ref("asc");
const collegeFilterList = ref(false);

onMounted(async () => {
  const email = userStore.user.email;

  const emailToCollegeMap = {
    "<EMAIL>":
      "Arts and Sciences, Engineering, Architecture, Computer Studies",
    "<EMAIL>": "Business, Commerce, Accountancy",
    "<EMAIL>": "Business, Commerce, Accountancy",
    "<EMAIL>": "Graduate School / Nursing",
    "<EMAIL>":
      "Tourism Management, Hotel Management, Education",
    "<EMAIL>": "Criminology / BS Psychology",
  };

  const fullAccessEmails = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ];

  const authorizedEmails = [
    ...fullAccessEmails,
    ...Object.keys(emailToCollegeMap),
  ];

  window.addEventListener("keydown", (e) => {
    if (e.key === "Escape" && showImageModal.value) {
      closeImageModal();
    }
  });

  if (userStore.user.isAuthenticated && authorizedEmails.includes(email)) {
    // Set selected college for non-full-access users
    if (!fullAccessEmails.includes(email)) {
      selectedCollege.value = emailToCollegeMap[email] || "";
    } else {
      // Enable college filter dropdown for full access users
      collegeFilterList.value = true;
    }

    await fetchListItems();
    router.push("/registrar/dashboard");
    startAutoRefresh();
  } else {
    router.push("/unauthorized");
  }
});

const refreshInterval = 1000;
let refreshTimer = null;
let isModalOpen = ref(false);
let isSelectingAll = ref(false);
const isQuietFetching = ref(false);

const startAutoRefresh = () => {
  // Clear any existing timer
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }

  // Set up new interval timer
  refreshTimer = setInterval(async () => {
    if (!checkIfModalOpen() && !isSelectingAll.value && !isConfirming.value && !isVerifying.value && !isQuietFetching.value) {
      await fetchListItemsQuietly();
      await checkAndRemoveDuplicates();
    }
  }, refreshInterval);
};

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

const checkAndRemoveDuplicates = async () => {
  stopAutoRefresh();
  try {
    // Get all items
    const items = listItems.value;
    if (!items || !Array.isArray(items) || items.length === 0) return;

    // Find duplicate tracking_ids
    const trackingIds = {};
    const duplicates = [];

    items.forEach((item) => {
      if (!item.tracking_id) return;

      if (trackingIds[item.tracking_id]) {
        // This is a duplicate, keep the one with the earlier created_at
        const existingItem = trackingIds[item.tracking_id];
        const duplicateToRemove =
          new Date(existingItem.created_at) > new Date(item.created_at)
            ? existingItem.id
            : item.id;

        duplicates.push(duplicateToRemove);
      } else {
        trackingIds[item.tracking_id] = item;
      }
    });

    // Delete duplicates
    for (const id of duplicates) {
      console.log(`Removing duplicate item with ID: ${id}`);
      await $fetch(endpoint.value + "/api/registrar/" + id + "/delete/", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });
    }

    // If any duplicates were removed, refresh the list
    if (duplicates.length > 0) {
      console.log(`Removed ${duplicates.length} duplicate entries`);
      await fetchListItems();
    }
  } catch (error) {
    console.error("Error checking for duplicates:", error);
  } finally {
    startAutoRefresh();
  }
};

// Add a reactive property to track verification status
const verificationStatuses = ref({});
const paymentStatuses = ref({});
const prepDocStatuses = ref({});
const releasingDocStatuses = ref({});
const transactionClosedStatuses = ref({});

// Function to update verification statuses
const updateVerificationStatuses = () => {
  if (!listItems.value || !Array.isArray(listItems.value)) return;

  listItems.value.forEach((item) => {
    if (item && item.id) {
      verificationStatuses.value[item.id] = hasReviewedAndVerifiedStatus(item);
    }
  });
};

const updatePaymentStatuses = () => {
  if (!listItems.value || !Array.isArray(listItems.value)) return;

  listItems.value.forEach((item) => {
    if (item && item.id) {
      paymentStatuses.value[item.id] = hasPaidStatus(item);
    }
  });
};
const updatePrepDocStatuses = () => {
  if (!listItems.value || !Array.isArray(listItems.value)) return;

  listItems.value.forEach((item) => {
    if (item && item.id) {
      prepDocStatuses.value[item.id] = hasPrepDocStatus(item);
    }
  });
};

const updateReleasingDocStatuses = () => {
  if (!listItems.value || !Array.isArray(listItems.value)) return;

  listItems.value.forEach((item) => {
    if (item && item.id) {
      releasingDocStatuses.value[item.id] = hasReleasingDocStatus(item);
    }
  });
};

const updateTransactionClosedStatuses = () => {
  if (!listItems.value || !Array.isArray(listItems.value)) return;

  listItems.value.forEach((item) => {
    if (item && item.id) {
      transactionClosedStatuses.value[item.id] =
        hasTransactionClosedStatus(item);
    }
  });
};

// Function to check if an item has "Reviewed and Verified" status
const hasReviewedAndVerifiedStatus = (item) => {
  if (
    !item ||
    !item.logs ||
    !Array.isArray(item.logs) ||
    item.logs.length === 0
  ) {
    return false;
  }

  return item.logs.some(
    (log) =>
      log.status_remarks && log.status_remarks.includes("Reviewed and Verified")
  );
};

const hasPaidStatus = (item) => {
  if (
    !item ||
    !item.logs ||
    !Array.isArray(item.logs) ||
    item.logs.length === 0
  ) {
    return false;
  }

  return item.logs.some(
    (log) => log.status_remarks && log.status_remarks.includes("Assessment")
  );
};

const hasPrepDocStatus = (item) => {
  if (
    !item ||
    !item.logs ||
    !Array.isArray(item.logs) ||
    item.logs.length === 0
  ) {
    return false;
  }

  return item.logs.some(
    (log) =>
      log.status_remarks && log.status_remarks.includes("Preparing Documents")
  );
};

const hasReleasingDocStatus = (item) => {
  if (
    !item ||
    !item.logs ||
    !Array.isArray(item.logs) ||
    item.logs.length === 0
  ) {
    return false;
  }

  return item.logs.some(
    (log) =>
      log.status_remarks && log.status_remarks.includes("Releasing Documents")
  );
};

const hasTransactionClosedStatus = (item) => {
  if (
    !item ||
    !item.logs ||
    !Array.isArray(item.logs) ||
    item.logs.length === 0
  ) {
    return false;
  }

  return item.logs.some(
    (log) =>
      log.status_remarks && log.status_remarks.includes("Transaction Closed")
  );
};

// Watch for changes in listItems
watch(
  () => listItems.value,
  () => {
    updateVerificationStatuses();
    updatePaymentStatuses();
    updatePrepDocStatuses();
    updateReleasingDocStatuses();
    updateTransactionClosedStatuses();
  },
  { deep: true }
);

const fetchListItemsQuietly = async () => {
  try {
    const updatedItems =
      (await $fetch(endpoint.value + "/api/registrar/list").catch(
        (error) => error.data
      )) || [];

    // Update the list items without affecting search or other UI state
    if (updatedItems.length > 0) {
      // If we have a search active, we need to preserve it
      if (originalListItems.value.length > 0) {
        // Update the original list
        originalListItems.value = updatedItems;

        // Re-apply the current search filter
        performSearch();
      } else {
        // No search active, just update the list
        listItems.value = updatedItems;
      }

      // Update verification statuses after updating the list
      updateVerificationStatuses();
      updatePaymentStatuses();
      updatePrepDocStatuses();
      updateReleasingDocStatuses();
      updateTransactionClosedStatuses();

      // If we have a current item open in a modal, make sure it stays updated
      if (currentItem.value) {
        const updatedCurrentItem = updatedItems.find(
          (item) => item.id === currentItem.value.id
        );
        if (updatedCurrentItem) {
          // Preserve modal state properties that might not be in the API response
          currentItem.value = {
            ...updatedCurrentItem,
            // Add any properties that need to be preserved here
          };
        }
      }
    }
  } catch (error) {
    console.error("Error fetching list items quietly:", error);
  }
};

const fetchListItems = async () => {
  isLoading.value = true;
  try {
    const response = await $fetch(endpoint.value + "/api/registrar/list").catch(
      (error) => {
        console.error("Fetch error:", error);
        return [];
      }
    );

    // Ensure listItems is always an array
    listItems.value = Array.isArray(response) ? response : [];

    // Reset search state
    originalListItems.value = [];
    searchQuery.value = "";

    // Update verification statuses
    updateVerificationStatuses();
    updatePaymentStatuses();
    updatePrepDocStatuses();
    updateReleasingDocStatuses();
    updateTransactionClosedStatuses();
  } catch (error) {
    console.error("Error fetching list items:", error);
    listItems.value = []; // fallback
  } finally {
    isLoading.value = false;
  }
};

onBeforeUnmount(() => {
  stopAutoRefresh();
});

const logOut = () => {
  router.push("/registrar/login");
  userStore.removeToken();
};

const selectedItems = ref([]);
const isDeleting = ref(false);

const allSelected = computed(() => {
  return (
    selectedItems.value.length === filteredByCollege.value.length &&
    filteredByCollege.value.length > 0
  );
});

const selectAllItems = () => {
  console.log("selectAllItems called");
  console.log("allSelected.value:", allSelected.value);
  stopAutoRefresh();
  isSelectingAll.value = true;
  if (allSelected.value) {
    // Deselect all
    selectedItems.value = [];
  } else {
    // Select all
    console.log("filteredByCollege.value:", filteredByCollege.value);
    selectedItems.value = filteredByCollege.value.map((item) => item.id);
  }
  console.log("selectedItems.value:", selectedItems.value);
  startAutoRefresh();
  isSelectingAll.value = false;
};

const toggleDeleteMultiple = () => {
  if (selectedItems.value.length === 0) return;
  toggleConfirmDelete.value = true;
  isModalOpen.value = true;
};

const cancelDelete = () => {
  toggleConfirmDelete.value = false;
  isModalOpen.value = checkIfModalOpen();
};

const deleteItems = async () => {
  if (selectedItems.value.length === 0) return;

  try {
    // Show loading state
    isDeleting.value = true;

    // Delete each selected item
    for (const id of selectedItems.value) {
      await $fetch(endpoint.value + "/api/registrar/" + id + "/delete/", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });
    }

    // Success handling
    console.log(`${selectedItems.value.length} items deleted successfully`);

    // Clear selection
    selectedItems.value = [];

    // Refresh the list
    await fetchListItems();

    // Close the modal
    toggleConfirmDelete.value = false;
    isModalOpen.value = checkIfModalOpen();
  } catch (error) {
    console.error("Error deleting items:", error);
  } finally {
    // Reset loading state
    isDeleting.value = false;
  }
};

let filteredItems;
const filteredListItems = computed(() => {
  let items = [...listItems.value];

  // Filter by status in logs
  if (statusFilter.value === "done") {
    items = items.filter((item) =>
      item.logs?.some((log) => {
        const statusRemarks = log?.status_remarks?.toLowerCase() || "";
        return statusRemarks.includes("transaction closed");
      })
    );
  } else if (statusFilter.value === "pending") {
    items = items.filter((item) =>
      item.logs?.every((log) => {
        const statusRemarks = log?.status_remarks?.toLowerCase() || "";
        return !statusRemarks.includes("transaction closed");
      })
    );
  }

  return _.orderBy(items, "created_at", "asc");
});

const doneCount = (college) => {
  return filteredListItems.value.filter(
    (item) =>
      item.college === college &&
      item.logs?.some((log) => {
        const statusRemarks = log?.status_remarks?.toLowerCase() || "";
        return statusRemarks.includes("transaction closed");
      })
  ).length;
};

const pendingCount = (college) => {
  return filteredListItems.value.filter(
    (item) =>
      item.college === college &&
      item.logs?.every((log) => {
        const statusRemarks = log?.status_remarks?.toLowerCase() || "";
        return !statusRemarks.includes("transaction closed");
      })
  ).length;
};

const totalCount = (college) => {
  return filteredListItems.value.filter((item) => item.college === college)
    .length;
};

const sortColleges = (column) => {
  if (sortColumn.value === column) {
    sortDirection.value = sortDirection.value === "asc" ? "desc" : "asc";
  } else {
    sortColumn.value = column;
    sortDirection.value = "asc";
  }

  colleges.value.sort((a, b) => {
    let valueA, valueB;

    if (column === "Pending") {
      valueA = pendingCount(a.value);
      valueB = pendingCount(b.value);
    } else if (column === "Done") {
      valueA = doneCount(a.value);
      valueB = doneCount(b.value);
    } else if (column === "Total") {
      valueA = totalCount(a.value);
      valueB = totalCount(b.value);
    } else {
      // Default to sorting by department label
      valueA = a.label;
      valueB = b.label;
    }

    if (valueA < valueB) {
      return sortDirection.value === "asc" ? -1 : 1;
    }
    if (valueA > valueB) {
      return sortDirection.value === "asc" ? 1 : -1;
    }
    return 0;
  });
};

const getEmailsForCollege = (college) => {
  const emailToCollegeMap = {
    "<EMAIL>":
      "Arts and Sciences, Engineering, Architecture, Computer Studies",
    "<EMAIL>": "Business, Commerce, Accountancy",
    "<EMAIL>": "Business, Commerce, Accountancy",
    "<EMAIL>": "Graduate School / Nursing",
    "<EMAIL>":
      "Tourism Management, Hotel Management, Education",
    "<EMAIL>": "Criminology / BS Psychology",
  };

  return Object.keys(emailToCollegeMap).filter(
    (email) => emailToCollegeMap[email] === college
  );
};

const isLoading = ref(true);
const currentPage = ref(1);
const itemsPerPage = 500;

const maxVisiblePages = 4;

const totalPages = computed(() => {
  return Math.ceil(filteredListItems.value.length / itemsPerPage);
});

const paginatedListItems = computed(() => {
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return filteredListItems.value.slice(startIndex, endIndex);
});

const filteredByCollege = computed(() => {
  console.log("filteredByCollege computed");
  console.log("selectedCollege.value:", selectedCollege.value);
  console.log("paginatedListItems.value:", paginatedListItems.value);
  if (!selectedCollege.value) {
    return paginatedListItems.value;
  }
  if (!selectedCollege.value) {
    return paginatedListItems.value;
  }
  const filtered = paginatedListItems.value.filter(
    (item) => item.college === selectedCollege.value
  );
  console.log("filtered:", filtered);
  return filtered;
});

const visiblePages = computed(() => {
  const pages = [];
  let startPage = Math.max(
    1,
    currentPage.value - Math.floor(maxVisiblePages / 2)
  );
  let endPage = Math.min(totalPages.value, startPage + maxVisiblePages - 1);

  if (endPage - startPage + 1 < maxVisiblePages) {
    startPage = Math.max(1, endPage - maxVisiblePages + 1);
  }

  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }
  return pages;
});

// const showPersonalInfoModal = ref(false);

onBeforeUnmount(() => {
  window.removeEventListener("keydown", (e) => {
    if (e.key === "Escape" && showImageModal.value) {
      closeImageModal();
    }
  });
  // Also make sure to clean up the auto-refresh
  stopAutoRefresh();
});

const searchQuery = ref("");
const originalListItems = ref([]);

const performSearch = () => {
  // Reset to page 1 when searching
  currentPage.value = 1;

  // Store original list if this is the first search
  if (originalListItems.value.length === 0 && listItems.value.length > 0) {
    originalListItems.value = [...listItems.value];
  }

  // Perform the search
  const query = searchQuery.value.toLowerCase().trim();

  // Filter the items based on search query
  const filteredItems =
    originalListItems.value.length > 0
      ? originalListItems.value
      : listItems.value;

  listItems.value = filteredItems.filter((item) => {
    // Search in all string properties of the item
    if (!item) return false;

    const query = searchQuery.value.toLowerCase().trim();

    // Check all properties of the item
    return Object.keys(item).some((key) => {
      // Only search in string properties
      if (typeof item[key] === "string") {
        return item[key].toLowerCase().includes(query);
      }
      return false;
    });
  });

  // Clear selection when search results change
  selectedItems.value = [];
};

// Add a computed property to sort logs by timestamp (oldest first)
const sortedLogs = computed(() => {
  if (!currentItem.value?.logs || !Array.isArray(currentItem.value.logs)) {
    return [];
  }

  // Create a copy of the logs array to avoid modifying the original
  return [...currentItem.value.logs].sort((a, b) => {
    // Parse timestamps using moment for accurate comparison
    const dateA = moment(a.timestamp, "MMMM DD, YYYY h:mm:ss A");
    const dateB = moment(b.timestamp, "MMMM DD, YYYY h:mm:ss A");

    // Sort in ascending order (oldest first)
    return dateA.valueOf() - dateB.valueOf();
  });
});

watch(listItems, (val) => {
  console.log("listItems updated:", val);
});

watch(selectedCollege, () => {
  selectedItems.value = [];
});

watch(statusFilter, () => {
  selectedItems.value = [];
});

const isFullAccess = computed(() => {
  return (
    userStore.user.email === "<EMAIL>" ||
    userStore.user.email === "<EMAIL>"
  );
});

// Dummy data structure
const currentItemInfo = ref({
  payment_id: "PID" + moment().valueOf(),
  fullname: "",
  email: "",
  course: "",
  college: "",
  date_graduated_last_attended: "",
  total: "0",
  tracking_id: "",
  detail_fees: [],
});

// Add this after the currentItemInfo ref declaration
const documentFees = ref({
  "Transcript of Records": 85,
  "Transfer of Credentials (Honorable Dismissal)": 0,
  "CAV (Certification, Authentication, Verification)": 0,
  "Credential Evaluations (WES, CGFNS, NCLEX, SpanTran, IES, etc.)": 0,
});

const addDetailFee = () => {
  currentItemInfo.value.detail_fees.push({ fee_name: "", amount: 0 });
  updateTotal();
};

// Remove fee row
const removeDetailFee = (index) => {
  currentItemInfo.value.detail_fees.splice(index, 1);
  updateTotal();
};

// Recalculate total
const updateTotal = () => {
  let total = 0;
  currentItemInfo.value.detail_fees.forEach((fee) => {
    total += Number((fee.amount || 0).toString());
  });
  const formatted = total.toFixed(2);
  currentItemInfo.value.total = formatted;
  // requestPaymentFee.value.total = formatted;
};

// Reactively update total when fee amounts change
watch(
  () => currentItemInfo.value.detail_fees,
  () => updateTotal(),
  { deep: true }
);

const hasEmptyFeeName = computed(() => {
  return currentItemInfo.value?.detail_fees?.some(
    (fee) => !fee.fee_name || fee.fee_name.trim() === ""
  );
});

const showSuccessMessage = ref(false);
const isSendingPayment = ref(false);

// Add this function to check if a document type is standard
const isStandardDocumentType = (docName) => {
  const standardTypes = [
    "Transcript of Records",
    "Transfer of Credentials (Honorable Dismissal)",
    "CAV (Certification, Authentication, Verification)",
    "Credential Evaluations (WES, CGFNS, NCLEX, SpanTran, IES, etc.)",
  ];

  return standardTypes.includes(docName);
};
</script>
<template>
  <div>
    <div class="h-screen flex">
      <div
        class="pb-3 lg:w-3/12 bg-gray-100 w-full flex overflow-hidden"
        v-show="toggleSideBarMenu"
      >
        <div class="w-full">
          <div
            class="flex items-center justify-center text-white bg-green-900 lg:py-[16px] py-[8px] sta"
          >
            <div class="flex items-center w-full px-2">
              <i class="fa fa-user mx-2" aria-hidden="true"></i>
              <h1 class="text-sm">
                {{ userStore.user.email }}
              </h1>
            </div>

            <div
              @click="toggleSideBarMenu = !toggleSideBarMenu"
              class="w-10 px-1.5 lg:hidden flex"
            >
              <i
                class="fa text-3xl text-white"
                :class="toggleSideBarMenu ? 'fa-caret-left' : 'fa-bars'"
                aria-hidden="true"
              ></i>
            </div>
          </div>

          <div class="">
            <div class="w-fit mx-auto mt-5 mb-3">
              <img
                src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/logos/LSU_Seal.PNG"
                class="lg:w-24 w-20 mx-auto"
              />
            </div>

            <div class="text-center">
              <h1 class="font-bold text-green-800 text-2xl">Dashboard</h1>
            </div>

            <div class="mx-auto mt-10 mb-5 grid grid-cols-1">
              <a
                href="/registrar/dashboard"
                class="text-xs mx-auto mb-2 w-full uppercase whitespace-nowrap px-5 py-1 font-bold text-left text-black hover:bg-black hover:text-white"
              >
                <i class="fa fa-list mr-3" aria-hidden="true"></i>
                All Request Lists
              </a>

              <a
                href="/"
                class="text-xs mx-auto mb-2 w-full uppercase whitespace-nowrap px-5 py-1 font-bold text-left text-green-900 hover:bg-green-900 hover:text-white"
              >
                <i class="fa fa-globe mr-3" aria-hidden="true"></i>
                LSU HOME PAGE
              </a>
            </div>
          </div>
        </div>
      </div>

      <div class="w-full overflow-y-scroll">
        <div class="bg-green-800 fixed w-full z-10">
          <div class="flex mx-auto justify-between py-2 px-3.5">
            <div
              @click="toggleSideBarMenu = !toggleSideBarMenu"
              class="w-auto flex items-center lg:px-1.5"
            >
              <i
                class="fa text-3xl text-white"
                :class="toggleSideBarMenu ? 'fa-caret-left' : 'fa-bars'"
                aria-hidden="true"
              ></i>
              <p
                class="text-white whitespace-nowrap lg:ml-5 ml-3 font-bold uppercase lg:text-sm text-xs"
              >
                Registrar
              </p>
            </div>

            <button @click="logOut" class="flex hover:font-bold pt-1">
              <i class="fa fa-sign-out text-white text-xl"></i>
              <h1 class="text-xs text-white p-1.5 lg:flex hidden">Log Out</h1>
            </button>
          </div>
        </div>

        <div
          v-if="isFullAccess"
          class="lg:mx-5 mx-2 shadow-lg border-2 border-green-600 rounded-lg mt-16 lg:mb-0 mb-3"
        >
          <div>
            <ul
              class="flex border-b lg:font-bold border-green-800 uppercase lg:text-sm text-[8px] py-2"
            >
              <li
                class="lg:w-7/12 border border-transparent w-full lg:px-5 text-left px-2"
              >
                Department
              </li>
              <li
                @click="sortColleges('Pending')"
                class="lg:w-2/12 w-3/12 text-center cursor-pointer"
              >
                Pending
                <i
                  :class="{
                    'fa fa-sort-up':
                      sortColumn === 'Pending' && sortDirection === 'asc',
                    'fa fa-sort-down':
                      sortColumn === 'Pending' && sortDirection === 'desc',
                    'fa fa-sort': sortColumn !== 'Pending',
                  }"
                ></i>
              </li>
              <li
                @click="sortColleges('Done')"
                class="lg:w-2/12 w-3/12 text-center cursor-pointer"
              >
                Done
                <i
                  :class="{
                    'fa fa-sort-up':
                      sortColumn === 'Done' && sortDirection === 'asc',
                    'fa fa-sort-down':
                      sortColumn === 'Done' && sortDirection === 'desc',
                    'fa fa-sort': sortColumn !== 'Done',
                  }"
                ></i>
              </li>
              <li
                @click="sortColleges('Total')"
                class="lg:w-2/12 w-3/12 text-center cursor-pointer"
              >
                Total
                <i
                  :class="{
                    'fa fa-sort-up':
                      sortColumn === 'Total' && sortDirection === 'asc',
                    'fa fa-sort-down':
                      sortColumn === 'Total' && sortDirection === 'desc',
                    'fa fa-sort': sortColumn !== 'Total',
                  }"
                ></i>
              </li>
            </ul>
          </div>
          <div>
            <ul>
              <li
                class="border-b"
                v-for="college in colleges"
                :key="college.value"
              >
                <ul class="flex items-center lg:text-sm text-xs">
                  <li class="lg:w-7/12 w-8/12 text-left lg:px-5 px-2 py-1">
                    {{ college.label }}
                    <div class="text-xs text-gray-500">
                      {{ getEmailsForCollege(college.value).join(", ") }}
                    </div>
                  </li>

                  <li class="w-2/12 text-center">
                    {{ pendingCount(college.value) }}
                  </li>
                  <li class="w-2/12 text-center">
                    {{ doneCount(college.value) }}
                  </li>
                  <li class="w-2/12 text-center">
                    {{ totalCount(college.value) }}
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </div>

        <div class="">
          <div class="w-full lg:p-5 px-2 py-2">
            <div v-show="tableDisplay" class="relative border">
              <div
                class="w-full shadow bg-gray-100 text-green-900 font-bold px-2 text-center mb-3 py-2 text-xs uppercase"
              >
                All Request Lists
              </div>

              <!-- Fullscreen Success Toast -->
              <div
                v-if="verifySuccessMessage"
                class="fixed inset-0 z-[1000] bg-gray-800 bg-opacity-90 flex items-center justify-center"
              >
                <p class="text-white text-lg font-semibold text-center px-6">
                  {{ verifySuccessMessage }}
                </p>
              </div>

              <div
                v-if="verifyErrorMessage"
                class="fixed inset-0 z-[1000] bg-red-900 bg-opacity-90 flex items-center justify-center"
              >
                <p class="text-white text-lg font-semibold text-center px-6">
                  {{ verifyErrorMessage }}
                </p>
              </div>
              <div
                class="w-full flex justify-between items-center mb-4 px-2 py-2 bg-white shadow rounded-md"
              >
                <div class="lg:flex justify-between w-full gap-x-3">
                  <div class="flex justify-between gap-x-2">
                    <div class="">
                      <button
                        @click="selectAllItems"
                        class="whitespace-nowrap flex items-center px-2 bg-gray-200 hover:bg-gray-300 text-gray-700 lg:px-3 pr-2 py-1.5 rounded-md transition-colors duration-200 lg:text-sm text-xs"
                      >
                        <!-- <i class="fa fa-check-square"></i> -->
                        {{ allSelected ? "Deselect All" : "Select All" }}
                      </button>
                    </div>

                    <div :class="selectedItems.length === 0 ? 'hidden' : ''">
                      <button
                        @click="toggleDeleteMultiple"
                        class="flex items-center hover:bg-red-600 text-white px-3 lg:py-1 py-0.5 rounded-md transition-colors duration-200 disabled:opacity-50 whitespace-nowrap"
                        :disabled="selectedItems.length === 0"
                        :class="
                          selectedItems.length === 0
                            ? 'bg-pink-200'
                            : 'bg-red-500'
                        "
                      >
                        <i class="fa fa-trash mr-2"></i>
                        ({{ selectedItems.length }})
                      </button>
                    </div>
                  </div>

                  <div
                    class="flex items-center lg:my-0 my-2"
                    v-if="collegeFilterList"
                  >
                    <select
                      v-model="selectedCollege"
                      class="lg:px-3 px-1 lg:py-1.5 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500 w-full text-xs"
                    >
                      <option value="">All Colleges</option>
                      <option
                        v-for="college in colleges"
                        :key="college.value"
                        :value="college.value"
                      >
                        {{ college.label }}
                      </option>
                    </select>
                  </div>

                  <div class="flex gap-x-2">
                    <button
                      @click="statusFilter = 'all'"
                      :class="[
                        'px-2 py-1 rounded-xl border',
                        statusFilter === 'all'
                          ? 'bg-blue-600 text-white border-blue-600'
                          : 'bg-white text-blue-600 border-blue-600 hover:bg-blue-600 hover:text-white',
                      ]"
                    >
                      All
                    </button>
                    <button
                      @click="statusFilter = 'pending'"
                      :class="[
                        'px-2 py-1 rounded-xl border',
                        statusFilter === 'pending'
                          ? 'bg-red-600 text-white border-red-600'
                          : 'bg-white text-red-600 border-red-600 hover:bg-red-600 hover:text-white',
                      ]"
                    >
                      Pending
                    </button>
                    <button
                      @click="statusFilter = 'done'"
                      :class="[
                        'px-2 py-1 rounded-xl border',
                        statusFilter === 'done'
                          ? 'bg-green-600 text-white border-green-600'
                          : 'bg-white text-green-600 border-green-600 hover:bg-green-600 hover:text-white',
                      ]"
                    >
                      Done
                    </button>
                  </div>

                  <div class="flex items-center w-full lg:mt-0 mt-2">
                    <div class="relative w-full">
                      <input
                        v-model="searchQuery"
                        type="search"
                        placeholder="Search"
                        class="lg:px-3 px-2 lg:py-1.5 py-1 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500 w-full text-xs"
                        @keyup.enter="performSearch"
                      />
                      <button
                        @click="performSearch"
                        class="absolute right-0 top-0 h-full px-3 text-gray-500 hover:text-green-600"
                      >
                        <i class="fa fa-search"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="">
                <div class="appointment-lists mx-auto text-xs">
                  <div v-if="isLoading" class="text-center">
                    <div class="">
                      <div class="flex animate-pulse space-x-4">
                        <div class="flex-1">
                          <div class="h-10 bg-gray-300"></div>
                          <div class="h-0.5 bg-gray-100"></div>
                          <div class="h-10 bg-gray-200"></div>
                          <div class="h-0.5 bg-gray-100"></div>
                          <div class="h-10 bg-gray-300"></div>
                          <div class="h-0.5 bg-gray-100"></div>
                          <div class="h-10 bg-gray-200"></div>
                          <div class="h-0.5 bg-gray-100"></div>
                          <div class="h-10 bg-gray-300"></div>
                          <div class="h-0.5 bg-gray-100"></div>
                          <div class="h-10 bg-gray-100"></div>
                          <div class="h-0.5 bg-gray-100"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div v-else>
                    <div class="gap-4" v-if="paginatedListItems.length > 0">
                      <div
                        class="flex items-center h-auto shadow lg:mb-0 mb-3 border-gray-200"
                        v-for="(b, i) in filteredByCollege"
                        :key="i"
                      >
                        <div class="w-fit flex px-2">
                          <div class="">
                            <input
                              type="checkbox"
                              :value="b.id"
                              v-model="selectedItems"
                              class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                            />
                          </div>
                        </div>

                        <div
                          class="w-full px-2 py-1.5 border-l lg:flex items-center"
                          :class="i % 2 ? 'bg-gray-100' : ''"
                        >
                          <div class="lg:w-4/12 w-full flex items-center">
                            <div>
                              <div class="w-full">
                                <div
                                  class="w-full lg:px-3 lg:flex items-center"
                                >
                                  <div
                                    class="text-[10px] font-light whitespace-nowrap"
                                  >
                                    <span class="mr-1">Tracking ID:</span>
                                    <span class="font-bold">
                                      {{ b.tracking_id }}
                                    </span>
                                  </div>
                                </div>
                              </div>

                              <div
                                class="w-full lg:block flex items-center lg:py-0 py-2 lg:px-3"
                                v-if="b.details"
                              >
                                <div class="text-[10px] font-light w-full flex">
                                  <span class="mr-4">Purpose:</span>
                                  <span class="font-bold flex">
                                    {{ b.details }}</span
                                  >
                                </div>
                              </div>
                            </div>
                          </div>

                          <div class="lg:w-4/12 w-full">
                            <div class="w-full my-1">
                              <div
                                class="w-full text-xs font-bold uppercase text-green-800 py-1 px-1 flex items-center"
                              >
                                <i class="fa fa-user mr-2.5"></i>
                                {{ b.lastname }}, {{ b.firstname }}
                                {{ b.middlename }}
                              </div>
                            </div>

                            <!-- Status Logs Modal -->
                            <div
                              v-if="showLogsModal && currentItem === b"
                              class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                              @click="closeLogsModal"
                            >
                              <div
                                class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full mx-4 transform transition-transform duration-300 max-h-[90vh] overflow-auto"
                                :class="{
                                  'scale-100 opacity-100': showLogsModal,
                                  'scale-95 opacity-0': !showLogsModal,
                                }"
                                @click.stop
                              >
                                <div
                                  class="flex justify-between items-center mb-4 border-b pb-3"
                                >
                                  <h3 class="text-lg font-medium text-gray-900">
                                    Status Logs -
                                    {{ currentItem?.tracking_id || "N/A" }}
                                  </h3>
                                  <button
                                    @click="closeLogsModal"
                                    class="text-gray-400 hover:text-gray-500"
                                  >
                                    <i class="fa fa-times"></i>
                                  </button>
                                </div>

                                <!-- Logs List - Update to sort by timestamp -->
                                <div class="mb-6 max-h-[40vh] overflow-y-auto">
                                  <div
                                    v-if="
                                      !currentItem?.logs ||
                                      currentItem.logs.length === 0
                                    "
                                    class="text-center text-gray-500 py-4"
                                  >
                                    No logs available
                                  </div>

                                  <div v-else class="space-y-3">
                                    <div
                                      v-for="(log, index) in sortedLogs"
                                      :key="index"
                                      class="border-l-4 rounded-r text-[10px]"
                                      :class="
                                        index === sortedLogs.length - 1
                                          ? 'border-green-500 bg-green-50 py-3 '
                                          : 'border-gray-300 bg-gray-50'
                                      "
                                    >
                                      <div
                                        class="flex items-center justify-between capitalize gap-x-3"
                                      >
                                        <div
                                          class="font-medium w-9/12"
                                          :class="
                                            index === sortedLogs.length - 1
                                              ? 'text-green-800 text-sm'
                                              : 'text-gray-500'
                                          "
                                        >
                                          <div
                                            class="w-full bg-transparent focus:outline-none px-1"
                                            :class="
                                              index === sortedLogs.length - 1
                                                ? 'border-green-300 focus:border-green-500'
                                                : 'border-gray-300 focus:border-gray-500'
                                            "
                                          >
                                            {{ log.status_remarks }}
                                          </div>
                                        </div>
                                        <div class="text-gray-500 w-3/12">
                                          <div
                                            class="whitespace-nowrap bg-transparent text-center -mb-0.5 focus:outline-none px-1 pb-0.5"
                                            :class="
                                              index === sortedLogs.length - 1
                                                ? 'border-green-300 focus:border-green-500'
                                                : 'border-gray-300 focus:border-gray-500'
                                            "
                                          >
                                            {{ log.timestamp }}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Personal Info Modal -->
                            <div
                              v-if="
                                showPersonalInfoModal &&
                                currentPersonalInfo === b
                              "
                              class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                              @click="closePersonalInfoModal"
                            >
                              <div
                                class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 transform transition-transform duration-300"
                                :class="{
                                  'scale-100 opacity-100':
                                    showPersonalInfoModal,
                                  'scale-95 opacity-0': !showPersonalInfoModal,
                                }"
                                @click.stop
                              >
                                <div
                                  class="flex justify-between items-center mb-4 border-b pb-3"
                                >
                                  <h3 class="text-lg font-medium text-gray-900">
                                    Personal Information
                                  </h3>
                                  <button
                                    @click="closePersonalInfoModal"
                                    class="text-gray-400 hover:text-gray-500"
                                  >
                                    <i class="fa fa-times"></i>
                                  </button>
                                </div>

                                <div class="">
                                  <!-- User Info -->
                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i class="fa fa-user text-green-600"></i>
                                    </div>
                                    <div class="ml-3 text-sm w-full">
                                      <p class="text-sm font-medium">
                                        Fullname
                                      </p>
                                      <p class="text-xs shadow-md py-1 px-2">
                                        {{ b.firstname }} {{ b.middlename }}
                                        {{ b.lastname }}
                                      </p>
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-bookmark text-green-600"
                                      ></i>
                                    </div>
                                    <div class="ml-3 text-sm w-full">
                                      <p class="text-sm font-medium">
                                        Tracking ID
                                      </p>
                                      <p class="text-xs shadow-md py-1 px-2">
                                        {{ b.tracking_id }}
                                      </p>
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-envelope text-green-600"
                                      ></i>
                                    </div>
                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        Email
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.email"
                                      />
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i class="fa fa-phone text-green-600"></i>
                                    </div>
                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        Contact Number
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.contact_number"
                                      />
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-birthday-cake text-green-500"
                                      ></i>
                                    </div>
                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        Date of Birth
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.birthdate"
                                      />
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-female text-green-500"
                                      ></i>
                                    </div>
                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        Mother's Maiden Name
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.mother_maiden_name"
                                      />
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-graduation-cap text-green-500"
                                      ></i>
                                    </div>

                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        ICC / LSU Graduate?
                                      </p>
                                      <select
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.alumni"
                                      >
                                        <option value="yes">Alumnus</option>
                                        <option value="no">Non-Alumnus</option>
                                      </select>
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-university text-green-500"
                                      ></i>
                                    </div>

                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        College
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.college"
                                      />
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i class="fa fa-book text-green-500"></i>
                                    </div>

                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        Course
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.course"
                                      />
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-calendar text-green-500"
                                      ></i>
                                    </div>

                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        Academic Year Graduated or Attended
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.year_graduated_last_attended"
                                      />
                                    </div>
                                  </div>

                                  <div class="w-full py-1">
                                    <div class="lg:flex items-center gap-x-3">
                                      <div
                                        class="font-bold text-[10px] mr-1 text-center"
                                      >
                                        IDs and Documents:
                                      </div>
                                      <div class="flex gap-x-3 w-fit mx-auto">
                                        <span
                                          class="flex items-center gap-x-3"
                                          v-if="b"
                                        >
                                          <a
                                            @click.prevent="
                                              openImageModal(
                                                b.valid_id_front[0].url.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif)/i
                                                )?.[0]
                                              )
                                            "
                                          >
                                            <img
                                              v-if="
                                                b.valid_id_front?.[0]?.url?.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif)/i
                                                )
                                              "
                                              :src="
                                                b.valid_id_front[0].url.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif)/i
                                                )?.[0]
                                              "
                                              class="cursor-pointer rounded-sm border-2 border-green-800 w-7 h-7 hover:opacity-80 transition-opacity"
                                            />
                                          </a>

                                          <a
                                            :href="b.valid_id_front?.[0]?.url.match(/https:\/\/.*?\.(pdf)/i)?.[0]"
                                            target="_blank"
                                            download
                                            v-if="
                                              b.valid_id_front?.[0]?.url?.match(
                                                /https:\/\/.*?\.(pdf)/i
                                              )
                                            "
                                          >
                                            <i
                                              class="fa fa-file-pdf text-red-700 text-2xl"
                                              aria-hidden="true"
                                            ></i>
                                          </a>

                                          <a
                                            :href="b.valid_id_front?.[0]?.url.match(/https:\/\/.*?\.(docx)/i)?.[0]"
                                            target="_blank"
                                            download
                                            v-if="
                                              b.valid_id_front?.[0]?.url?.match(
                                                /https:\/\/.*?\.(docx)/i
                                              )
                                            "
                                          >
                                            <i
                                              class="fa fa-file text-blue-800 text-2xl"
                                              aria-hidden="true"
                                            ></i>
                                          </a>

                                          <a
                                            @click.prevent="
                                              openImageModal(
                                                b.valid_id_back[0].url.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif)/i
                                                )?.[0]
                                              )
                                            "
                                          >
                                            <img
                                              v-if="
                                                b.valid_id_back?.[0]?.url?.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif)/i
                                                )
                                              "
                                              :src="
                                                b.valid_id_back[0].url.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif)/i
                                                )?.[0]
                                              "
                                              class="cursor-pointer rounded-sm border-2 border-green-800 w-7 h-7 hover:opacity-80 transition-opacity"
                                            />
                                          </a>

                                          <a
                                            :href="b.valid_id_back?.[0]?.url.match(/https:\/\/.*?\.(pdf)/i)?.[0]"
                                            target="_blank"
                                            download
                                            v-if="
                                              b.valid_id_back?.[0]?.url?.match(
                                                /https:\/\/.*?\.(pdf)/i
                                              )
                                            "
                                          >
                                            <i
                                              class="fa fa-file-pdf text-red-700 text-2xl"
                                              aria-hidden="true"
                                            ></i>
                                          </a>

                                          <a
                                            :href="b.valid_id_back?.[0]?.url.match(/https:\/\/.*?\.(docx)/i)?.[0]"
                                            target="_blank"
                                            download
                                            v-if="
                                              b.valid_id_back?.[0]?.url?.match(
                                                /https:\/\/.*?\.(docx)/i
                                              )
                                            "
                                          >
                                            <i
                                              class="fa fa-file text-blue-800 text-2xl"
                                              aria-hidden="true"
                                            ></i>
                                          </a>

                                          <a
                                            @click.prevent="
                                              openImageModal(
                                                b.credential_evaluation_requests[0].url.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif)/i
                                                )?.[0]
                                              )
                                            "
                                          >
                                            <img
                                              v-if="
                                                b.credential_evaluation_requests?.[0]?.url?.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif)/i
                                                )
                                              "
                                              :src="
                                                b.credential_evaluation_requests[0].url.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif)/i
                                                )?.[0]
                                              "
                                              class="cursor-pointer rounded-sm border-2 border-green-800 w-7 h-7 hover:opacity-80 transition-opacity"
                                            />
                                          </a>

                                          <a
                                            :href="
                                              b
                                                .credential_evaluation_requests?.[0]
                                                ?.url.match(/https:\/\/.*?\.(pdf)/i)?.[0]
                                            "
                                            target="_blank"
                                            download
                                            v-if="
                                              b.credential_evaluation_requests?.[0]?.url?.match(
                                                /https:\/\/.*?\.(pdf)/i
                                              )
                                            "
                                          >
                                            <i
                                              class="fa fa-file-pdf text-red-700 text-2xl"
                                              aria-hidden="true"
                                            ></i>
                                          </a>

                                          <a
                                            :href="
                                              b
                                                .credential_evaluation_requests?.[0]
                                                ?.url.match(/https:\/\/.*?\.(docx)/i)?.[0]
                                            "
                                            target="_blank"
                                            download
                                            v-if="
                                              b.credential_evaluation_requests?.[0]?.url?.match(
                                                /https:\/\/.*?\.(docx)/i
                                              )
                                            "
                                          >
                                            <i
                                              class="fa fa-file text-blue-800 text-2xl"
                                              aria-hidden="true"
                                            ></i>
                                          </a>
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Verified Button -->
                                <div
                                  @click="verifiedConfirm(b)"
                                  class="w-full text-center px-2 py-1 font-bold hover:bg-green-600 hover:text-white rounded-md mt-2 cursor-pointer bg-white text-green-600 border border-green-600"
                                  :class="{
                                    'opacity-75 cursor-not-allowed':
                                      isVerifying,
                                  }"
                                >
                                  <span v-if="isVerifying">
                                    <i class="fa fa-spinner fa-spin mr-1"></i>
                                    Verifying...
                                  </span>
                                  <span v-else>Verify</span>
                                </div>
                              </div>
                            </div>

                            <!--Payment Info Modal -->

                            <!-- <Transition name="fade">
                              <div
                                v-if="showDuplicateAlertModal"
                                class="fixed inset-0 z-[100] flex items-center justify-center bg-black bg-opacity-30"
                              >
                                <div
                                  class="bg-white rounded-lg shadow-xl p-6 w-full max-w-sm mx-4 text-center"
                                >
                                  <h3
                                    class="text-lg font-semibold text-red-600 mb-3"
                                  >
                                    Duplicate Status
                                  </h3>
                                  <p class="text-sm text-gray-700 mb-4">
                                    This status has already been added as the
                                    most recent update.
                                  </p>
                                  <button
                                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm"
                                    @click="showDuplicateAlertModal = false"
                                  >
                                    OK
                                  </button>
                                </div>
                              </div>
                            </Transition> -->

                            <div
                              v-if="showPaymentModal && currentItem === b"
                              class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                              @click="closePaymentModal"
                            >
                              <div
                                class="bg-white rounded-lg shadow-xl p-6 w-full mx-4 transform transition-transform duration-300 max-h-[90vh] overflow-auto"
                                :class="{
                                  'scale-100 opacity-100': showPaymentModal,
                                  'scale-95 opacity-0': !showPaymentModal,
                                }"
                                @click.stop
                              >
                                <div
                                  class="flex justify-between items-center mb-4 border-b pb-3"
                                >
                                  <h3 class="text-lg font-medium text-gray-900">
                                    Payment Information
                                  </h3>
                                  <button
                                    @click="closePaymentModal"
                                    class="text-gray-400 hover:text-gray-500"
                                  >
                                    <i class="fa fa-times"></i>
                                  </button>
                                </div>

                                <div
                                  class="border-4 border-[#eeeeee] px-3 py-3 my-3 rounded-lg"
                                >
                                  <div class="flex w-full gap-x-2">
                                    <div
                                      class="flex items-center mb-2 w-full lg:w-6/12"
                                    >
                                      <div class="text-sm w-full">
                                        <label
                                          class="text-xs text-gray-900 whitespace-nowrap"
                                          >Department / College</label
                                        >
                                        <input
                                          type="text"
                                          v-model="currentItemInfo.college"
                                          class="w-full text-xs shadow-md py-1 px-2 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-green-500"
                                        />
                                      </div>
                                    </div>

                                    <div class="flex items-center mb-2 w-full">
                                      <div class="text-sm w-full">
                                        <label class="text-xs text-gray-900"
                                          >Course</label
                                        >
                                        <input
                                          type="text"
                                          v-model="currentItemInfo.course"
                                          class="w-full text-xs shadow-md py-1 px-2 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-green-500"
                                        />
                                      </div>
                                    </div>

                                    <!-- Date Graduated/Last Attended -->
                                    <div
                                      class="flex items-center mb-2 lg:w-fit w-full"
                                    >
                                      <div class="text-sm w-full">
                                        <label
                                          class="text-xs text-gray-900 whitespace-nowrap"
                                          >Date Graduated/Last Attended</label
                                        >
                                        <input
                                          type="date"
                                          v-model="
                                            currentItemInfo.date_graduated_last_attended
                                          "
                                          class="w-full text-xs shadow-md py-1 px-2 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-green-500"
                                          placeholder="Month Day Year"
                                        />
                                      </div>
                                    </div>

                                    <!-- Total -->
                                    <div class="flex items-center mb-2">
                                      <div class="text-sm w-full">
                                        <label class="text-xs text-gray-900"
                                          >Total</label
                                        >
                                        <input
                                          type="number"
                                          v-model="currentItemInfo.total"
                                          class="w-full text-xs shadow-md py-1 px-2 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-green-500"
                                        />
                                      </div>
                                    </div>
                                  </div>

                                  <!-- Detail Fees -->
                                  <div
                                    class="flex flex-col items-start w-fit mx-auto"
                                  >
                                    <div class="text-sm w-full">
                                      <p
                                        class="font-medium text-gray-900 text-center py-2"
                                      >
                                        Detail Fees
                                      </p>

                                      <div
                                        v-for="(
                                          fee, index
                                        ) in currentItemInfo?.detail_fees"
                                        :key="index"
                                        class="flex items-center space-x-2 mb-1"
                                      >
                                        <label
                                          :for="'fee_name_' + index"
                                          class="text-xs font-medium text-gray-700"
                                        >
                                          Fee Name:
                                        </label>
                                        <!-- :readonly="isStandardDocumentType(
                                            fee.fee_name
                                          ) && fee.fee_name !== ''
                                            "  -->
                                        <input
                                          type="text"
                                          :id="'fee_name_' + index"
                                          v-model="fee.fee_name"
                                          class="text-xs shadow-md py-1 px-2 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500"
                                        />

                                        <label
                                          :for="'fee_amount_' + index"
                                          class="text-xs font-medium text-gray-700"
                                        >
                                          Amount:
                                        </label>
                                        <!--
                                        :readonly="
                                            isStandardDocumentType(fee.fee_name)
                                          "
                                          :disabled="
                                            isStandardDocumentType(fee.fee_name)
                                          " -->
                                        <!-- :class="
                                            isStandardDocumentType(fee.fee_name)
                                              ? 'bg-gray-100'
                                              : ''
                                          " -->

                                        <input
                                          type="number"
                                          :id="'fee_amount_' + index"
                                          v-model="fee.amount"
                                          class="text-xs shadow-md py-1 px-2 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500"
                                        />

                                        <button
                                          @click="removeDetailFee(index)"
                                          class="px-1.5 py-1 text-red-500 hover:text-white text-xs bg-white border border-red-600 rounded-md hover:bg-red-600"
                                        >
                                          <i class="fa fa-close"></i>
                                        </button>
                                      </div>
                                    </div>

                                    <button
                                      @click="addDetailFee"
                                      class="mx-auto px-3 py-1 text-sm bg-green-500 text-white rounded-md hover:bg-green-600 mt-5"
                                    >
                                      Add Fee
                                    </button>
                                  </div>

                                  <!-- Submit Button -->
                                  <!-- Submit Button -->
                                  <div class="mt-4 mx-auto w-fit block">
                                    <button
                                      @click="sendEmailDetailFee(b)"
                                      :disabled="
                                        hasEmptyFeeName || isSendingPayment
                                      "
                                      class="px-5 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                                    >
                                      <template v-if="isSendingPayment">
                                        <svg
                                          class="animate-spin h-4 w-4 text-white"
                                          xmlns="http://www.w3.org/2000/svg"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                        >
                                          <circle
                                            class="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            stroke-width="4"
                                          ></circle>
                                          <path
                                            class="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8v8z"
                                          ></path>
                                        </svg>
                                        Sending...
                                      </template>
                                      <template v-else> Send Payment </template>
                                    </button>
                                  </div>
                                  <!-- Success Message -->
                                  <div
                                    v-if="showSuccessMessage"
                                    class="mt-3 text-center text-sm bg-green-100 text-green-800 border border-green-400 px-4 py-2 rounded w-full"
                                  >
                                    ✅ Payment details sent successfully!
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div
                              v-if="showLogsModal && currentItem === b"
                              class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                              @click="closeLogsModal"
                            >
                              <div
                                class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full mx-4 transform transition-transform duration-300 max-h-[90vh] overflow-auto"
                                :class="{
                                  'scale-100 opacity-100': showLogsModal,
                                  'scale-95 opacity-0': !showLogsModal,
                                }"
                                @click.stop
                              >
                                <div
                                  class="flex justify-between items-center mb-4 border-b pb-3"
                                >
                                  <h3 class="text-lg font-medium text-gray-900">
                                    Status Logs -
                                    {{ currentItem?.tracking_id || "N/A" }}
                                  </h3>
                                  <button
                                    @click="closeLogsModal"
                                    class="text-gray-400 hover:text-gray-500"
                                  >
                                    <i class="fa fa-times"></i>
                                  </button>
                                </div>

                                <!-- Logs List - Update to sort by timestamp -->
                                <div class="mb-6 max-h-[40vh] overflow-y-auto">
                                  <div
                                    v-if="
                                      !currentItem?.logs ||
                                      currentItem.logs.length === 0
                                    "
                                    class="text-center text-gray-500 py-4"
                                  >
                                    No logs available
                                  </div>

                                  <div v-else class="space-y-3">
                                    <div
                                      v-for="(log, index) in sortedLogs"
                                      :key="index"
                                      class="border-l-4 rounded-r text-[10px]"
                                      :class="
                                        index === sortedLogs.length - 1
                                          ? 'border-green-500 bg-green-50 py-3 '
                                          : 'border-gray-300 bg-gray-50'
                                      "
                                    >
                                      <div
                                        class="flex items-center justify-between capitalize gap-x-3"
                                      >
                                        <div
                                          class="font-medium w-9/12"
                                          :class="
                                            index === sortedLogs.length - 1
                                              ? 'text-green-800 text-sm'
                                              : 'text-gray-500'
                                          "
                                        >
                                          <div
                                            class="w-full bg-transparent focus:outline-none px-1"
                                            :class="
                                              index === sortedLogs.length - 1
                                                ? 'border-green-300 focus:border-green-500'
                                                : 'border-gray-300 focus:border-gray-500'
                                            "
                                          >
                                            {{ log.status_remarks }}
                                          </div>
                                        </div>
                                        <div class="text-gray-500 w-3/12">
                                          <div
                                            class="whitespace-nowrap bg-transparent text-center -mb-0.5 focus:outline-none px-1 pb-0.5"
                                            :class="
                                              index === sortedLogs.length - 1
                                                ? 'border-green-300 focus:border-green-500'
                                                : 'border-gray-300 focus:border-gray-500'
                                            "
                                          >
                                            {{ log.timestamp }}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Preparing Documents Modal -->
                          <div
                            v-if="showPrepDocModal && currentItem === b"
                            class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                            @click="closePrepDocModal"
                          >
                            <div
                              class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 transform transition-transform duration-300"
                              :class="{
                                'scale-100 opacity-100': showPrepDocModal,
                                'scale-95 opacity-0': !showPrepDocModal,
                              }"
                              @click.stop
                            >
                              <div
                                class="flex justify-between items-center mb-4 border-b pb-3"
                              >
                                <h3 class="text-lg font-medium text-gray-900">
                                  Preparing Documents
                                </h3>
                                <button
                                  @click="closePrepDocModal"
                                  class="text-gray-400 hover:text-gray-500"
                                >
                                  <i class="fa fa-times"></i>
                                </button>
                              </div>
                              <div class="flex items-center">
                                <div class="ml-3 text-sm w-full">
                                  <p class="font-medium text-gray-900">
                                    Follow Up Remarks:
                                  </p>
                                  <input
                                    type="text"
                                    class="text-xs shadow-md py-1 px-2 w-full"
                                    placeholder="Follow Up Remarks"
                                    v-model="b.follow_up_remarks"
                                  />
                                </div>
                              </div>

                              <!-- Verified Button -->
                              <div
                                @click="confirmPrepDoc(b)"
                                class="w-full text-center px-2 py-1 font-bold hover:bg-green-600 hover:text-white rounded-md mt-2 cursor-pointer bg-white text-green-600 border border-green-600"
                                :class="{
                                  'opacity-75 cursor-not-allowed': isConfirming,
                                }"
                              >
                                <span v-if="isConfirming">
                                  <i class="fa fa-spinner fa-spin mr-1"></i>
                                  Confirming...
                                </span>
                                <span v-else>Confirm</span>
                              </div>
                            </div>
                          </div>

                          <!-- Releasing Documents Modal -->
                          <div
                            v-if="showReleasingDocModal && currentItem === b"
                            class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                            @click="closeReleasingDocModal"
                          >
                            <div
                              class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 transform transition-transform duration-300"
                              :class="{
                                'scale-100 opacity-100': showReleasingDocModal,
                                'scale-95 opacity-0': !showReleasingDocModal,
                              }"
                              @click.stop
                            >
                              <div
                                class="flex justify-between items-center mb-4 border-b pb-3"
                              >
                                <h3 class="text-lg font-medium text-gray-900">
                                  Releasing Documents
                                </h3>
                                <button
                                  @click="closeReleasingDocModal"
                                  class="text-gray-400 hover:text-gray-500"
                                >
                                  <i class="fa fa-times"></i>
                                </button>
                              </div>

                              <!-- Verified Button -->
                              <div
                                @click="confirmReleasingDoc(b)"
                                class="w-full text-center px-2 py-1 font-bold hover:bg-green-600 hover:text-white rounded-md mt-2 cursor-pointer bg-white text-green-600 border border-green-600"
                                :class="{
                                  'opacity-75 cursor-not-allowed': isConfirming,
                                }"
                              >
                                <span v-if="isConfirming">
                                  <i class="fa fa-spinner fa-spin mr-1"></i>
                                  Confirming...
                                </span>
                                <span v-else>Confirm</span>
                              </div>
                            </div>
                          </div>
                          <!-- Transaction Closed Modal -->
                          <div
                            v-if="
                              showTransactionClosedModal && currentItem === b
                            "
                            class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                            @click="closeTransactionClosedModal"
                          >
                            <div
                              class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 transform transition-transform duration-300"
                              :class="{
                                'scale-100 opacity-100':
                                  showTransactionClosedModal,
                                'scale-95 opacity-0':
                                  !showTransactionClosedModal,
                              }"
                              @click.stop
                            >
                              <div
                                class="flex justify-between items-center mb-4 border-b pb-3"
                              >
                                <h3 class="text-lg font-medium text-gray-900">
                                  Transaction Closed
                                </h3>
                                <button
                                  @click="closeTransactionClosedModal"
                                  class="text-gray-400 hover:text-gray-500"
                                >
                                  <i class="fa fa-times"></i>
                                </button>
                              </div>

                              <!-- Verified Button -->
                              <div
                                @click="confirmTransactionClosed(b)"
                                class="w-full text-center px-2 py-1 font-bold hover:bg-green-600 hover:text-white rounded-md mt-2 cursor-pointer bg-white text-green-600 border border-green-600"
                                :class="{
                                  'opacity-75 cursor-not-allowed': isConfirming,
                                }"
                              >
                                <span v-if="isConfirming">
                                  <i class="fa fa-spinner fa-spin mr-1"></i>
                                  Confirming...
                                </span>
                                <span v-else>Confirm</span>
                              </div>
                            </div>
                          </div>

                          <div class="w-full">
                            <div class="w-fit mx-auto">
                              <div
                                class="lg:w-fit w-full py-1 lg:gap-x-3 lg:flex lg:whitespace-nowrap text-left"
                              >
                                <button
                                  @click="openPersonalInfoModal(b)"
                                  class="w-full lg:text-center text-left px-3 py-1 cursor-pointer border-b shadow-lg border-green-500 hover:bg-green-800 hover:text-white"
                                  :class="
                                    verificationStatuses[b.id]
                                      ? 'bg-green-800 text-white'
                                      : 'bg-white text-green-700'
                                  "
                                >
                                  1. Verify Info
                                </button>
                                <button
                                  class="w-full lg:text-center text-left px-3 py-1 cursor-pointer border-b shadow-lg border-green-500 hover:bg-green-800 hover:text-white"
                                  @click="openPaymentModal(b)"
                                  :class="
                                    paymentStatuses[b.id]
                                      ? 'bg-green-800 text-white'
                                      : 'bg-white text-green-700'
                                  "
                                >
                                  2. Assessment
                                </button>
                                <button
                                  @click="openPrepDocModal(b)"
                                  :class="
                                    prepDocStatuses[b.id]
                                      ? 'bg-green-800 text-white'
                                      : 'bg-white text-green-700'
                                  "
                                  class="w-full lg:text-center text-left px-3 py-1 cursor-pointer border-b shadow-lg border-green-500 hover:bg-green-800 hover:text-white"
                                >
                                  3. Preparing Documents
                                </button>
                                <button
                                  @click="openReleasingDocModal(b)"
                                  :class="
                                    releasingDocStatuses[b.id]
                                      ? 'bg-green-800 text-white'
                                      : 'bg-white text-green-700'
                                  "
                                  class="w-full lg:text-center text-left px-3 py-1 cursor-pointer border-b shadow-lg border-green-500 hover:bg-green-800 hover:text-white"
                                >
                                  4. Releasing Documents
                                </button>
                                <!-- (For Pickup or Shipment) -->
                                <button
                                  @click="openTransactionClosedModal(b)"
                                  :class="
                                    transactionClosedStatuses[b.id]
                                      ? 'bg-green-800 text-white'
                                      : 'bg-white text-green-700'
                                  "
                                  class="w-full lg:text-center text-left px-3 py-1 cursor-pointer border-b shadow-lg border-green-500 hover:bg-green-800 hover:text-white"
                                >
                                  5. Transaction Closed
                                </button>
                                <!-- @click="
                                    $nextTick(() => {
                                      if (!checkIfModalOpen()) {
                                        openLogsModal(b);
                                      }
                                    })
                                  " -->
                                <button
                                  class="lg:w-fit w-full px-2 py-1 text-green-700 cursor-pointer bg-white border-b shadow-lg border-green-500 hover:bg-green-800 hover:text-white"
                                  @click="openLogsModal(b)"
                                >
                                  <!-- Status Logs  -->
                                  <i class="fa fa-ellipsis-h"></i>
                                  <!-- ({{ b.logs ? b.logs.length : 0 }}) -->
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Image Modal -->
                      <div
                        v-if="showImageModal"
                        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
                        @click="closeImageModal"
                      >
                        <div
                          class="relative max-w-4xl max-h-[90vh] overflow-auto bg-white p-2 rounded-lg"
                          @click.stop
                        >
                          <button
                            @click="closeImageModal"
                            class="absolute top-2 right-2 text-gray-700 hover:text-red-500 bg-white rounded-full w-8 h-8 flex items-center justify-center shadow-md"
                          >
                            <i class="fa fa-close"></i>
                          </button>
                          <img
                            :src="currentModalImage"
                            class="max-w-full max-h-[85vh] object-contain"
                          />
                        </div>
                      </div>

                      <!-- Improved Delete Confirmation Modal -->
                      <div
                        v-if="toggleConfirmDelete"
                        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                      >
                        <div
                          class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 transform transition-transform duration-300"
                          :class="{
                            'scale-100 opacity-100': toggleConfirmDelete,
                            'scale-95 opacity-0': !toggleConfirmDelete,
                          }"
                        >
                          <div class="text-center">
                            <div
                              class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4"
                            >
                              <i
                                class="fa fa-exclamation-triangle text-red-600 text-xl"
                              ></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">
                              Confirm Deletion
                            </h3>
                            <p class="text-sm text-gray-500 mb-6">
                              Are you sure you want to delete
                              {{
                                selectedItems.length === 1
                                  ? "this record"
                                  : "these " +
                                    selectedItems.length +
                                    " records"
                              }}? This action cannot be undone.
                            </p>
                          </div>
                          <div class="flex justify-center gap-4">
                            <button
                              @click="deleteItems"
                              class="inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2 transition-colors duration-200"
                              :disabled="isDeleting"
                            >
                              <i
                                class="fa fa-spinner fa-spin mr-2"
                                v-if="isDeleting"
                              ></i>
                              {{ isDeleting ? "Deleting..." : "Delete" }}
                            </button>
                            <button
                              @click="cancelDelete"
                              class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2 transition-colors duration-200"
                              :disabled="isDeleting"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div
                      class="flex justify-center my-4 pb-10"
                      v-if="filteredListItems.length > 0"
                    >
                      <button
                        :disabled="currentPage === 1"
                        @click="currentPage--"
                        class="px-2 py-2 mx-1 bg-gray-200 rounded hover:bg-gray-500 hover:text-white"
                      >
                        Prev
                      </button>

                      <button
                        v-for="page in visiblePages"
                        :key="page"
                        @click="currentPage = page"
                        class="hover:bg-green-500 hover:text-white"
                        :class="{
                          'px-4 py-2 mx-1 rounded': true,
                          'bg-green-800 text-white': currentPage === page,
                          'bg-gray-200': currentPage !== page,
                        }"
                      >
                        {{ page }}
                      </button>

                      <button
                        :disabled="currentPage === totalPages"
                        @click="currentPage++"
                        class="px-2 py-2 mx-1 bg-gray-200 rounded hover:bg-gray-500 hover:text-white"
                      >
                        Next
                      </button>
                    </div>

                    <div v-else-if="!isLoading" class="text-center my-5">
                      No items found!
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="fixed bottom-0 w-full">
      <DashboardFooter />
    </div>
  </div>
</template>
<style lang="scss" scoped></style>
