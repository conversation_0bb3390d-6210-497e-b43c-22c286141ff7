<script setup>
  // Higher Education - Undergraduate Programs
  const newStudent = {
    id: '',
    confirmationID: '',
    student: {
      lsuID: '',
      title: '',
      firstName: '',
      middleName: '',
      lastName: '',
      extensionOrSuffixName: '',
      dateOfBirth: '',
      age: '',
      birthOrder: '',
      temporaryIDNumber: '',
      citizenship: '',
      birthPlace: '',
      biologicalSex: '',
      nationality: '',
      religion: '',
      // student current educational info
      // curriculumYearLevel: '',
      // new (second course), new (SHS graduate), new (HS Graduate), new (ALS Graduate), continuing, shiftee, returnee, second course, transferee
      academicStatus: '',
      // REGISTRATION  TO UNDERGRADUATE PROGRAMS FOR COLLEGE GRADUATES (SECOND COURSE)
      //namePreviousCollegeOrUniversity: '',
      //previousProgram: '',
      yearGraduatedPreviousCollegeOrUniversityOrSchool: '',
      //if continuing
      // lsuIDNumber: '',
      // single, married, widowed, separated, divorced
      civilStatus: '',
      //programApplied: '',
      dateApplied: '',
      semesterOrTerm: '',
      academicYear: '',
      dateOfBirth: '',
      gender: '',
      contactNumber: '',
      learningReferenceNumberReportCard: '',
      college: '',
      courseProgram: '',
      courseYearLevel: '',
      courseMajor: '',
      lsuEmailAddress: '',
      contactEmailAddress: '',
      // Please enter your permanent address in this format: Street, Purok, Barangay/Village, City/Municipality, Zip Code, Province/State, Country
      studentCurrentAddress: '',
      studentPermanentAddress: '',
      //own house, boarding house, with a relative, others(please specify)
      studentLivingHomeAddressCategory: '',
    },
    studentAccountInformation: {
      amountDueOrOutstandingBalance: '',
      amountPartialPayment: ''
    },
    studentContactPerson: {
      lastName: '',
      firstName: '',
      middleName: '',
      extensionOrSuffix: '',
      // Father, Mother, Legal legalGuardian/Spouse (if applicable), others (please specify below)
      relation: '',
      // Please enter your permanent address in this format: Street, Purok, Barangay/Village, City/Municipality, Zip Code, Province, Country
      permamentAddress: '',
      currentAddress: '',
      contactNumber: '',
    },
    //Living or Deceased
    father: {
      vitalLifeStatus: '',
      lastName: '',
      firstName: '',
      middleName: '',
      extensionOrSuffix: '',
      maritalStatus: '',
      dateOfBirth: '',
      permanentAddress: '',
      currentAddress: '',
      contactNumber: '',
      contactEmailAddress: '',
      employmentInfo: '',
      // Doctorate Graduate, Master's Graduate, College Graduate, Senior High School Graduate, Vocational Graduate, Highschool Graduate, Elementary Graduate, Preschool Graduate, NOT APPLICABLE (did not receive any formal education)
      highestEducationCompleted: '',
      occupationOrTitle: '',
      citizenship: '',
      // Allowance, Business, Commission, Donations/Contributions and the like, Interest on Savings/Placements/Investments, Pension, Salary, NOT IN THE LIST, NOT APPLICABLE (economically inactive; unemployed or no business), others
      sourceIncome: '',
      // NOT APPLICABLE (economically inactive; unemployed or no business), below 10k, 10k to 20k, 20k to 30k , 30k to 40k , 40k to 50k, 50k and advance
      fatherGrossMonthlyIncome: '',
      fatherCompanyEmployer: {
        name: '',
        address: '',
        contactNumber: ''
      },
    },
    mother: {
      VitalLifeStatus: '',
      LastName: '',
      FirstName: '',
      MiddleName: '',
      ExtensionOrSuffix: '',
      MaritalStatus: '',
      DateOfBirth: '',
      PermanentAddress: '',
      CurrentAddress: '',
      ContactNumber: '',
      ContactEmailAddress: '',
      EmploymentInfo: '',
      // Doctorate Graduate, Master's Graduate, College Graduate, Senior High School Graduate, Vocational Graduate, Highschool Graduate, Elementary Graduate, Preschool Graduate, NOT APPLICABLE (did not receive any formal education)
      HighestEducationCompleted: '',
      OccupationOrTitle: '',
      Citizenship: '',
      // Allowance, Business, Commission, Donations/Contributions and the like, Interest on Savings/Placements/Investments, Pension, Salary, NOT IN THE LIST, NOT APPLICABLE (economically inactive; unemployed or no business)
      SourceIncome: '',
      // NOT APPLICABLE (economically inactive; unemployed or no business), below 10k, 10k to 20k, 20k to 30k , 30k to 40k , 40k to 50k, 50k and advance
      GrossMonthlyIncome: '',
      CompanyEmployer: {
        name: '',
        address: '',
        contactNumber: ''
      },
    },
    //Internally or Externally
    hasFundedScholarshipOrGrantInAIDProgram: '',
    legalGuardianRelation: '',
    //Living or Deceased
    legalGuardianVitalLifeStatus: '',
    legalGuardianLastName: '',
    legalGuardianFirstName: '',
    legalGuardianMiddleName: '',
    legalGuardianextensionOrSuffix: '',
    legalGuardianMaritalStatus: '',
    legalGuardianDateOfBirth: '',
    legalGuardianPermanentAddress: '',
    legalGuardianCurrentAddress: '',
    legalGuardianContactNumber: '',
    legalGuardianContactEmailAddress: '',
    legalGuardianCitizenship: '',
    legalGuardianEmploymentInfo: '',
    // Doctorate Graduate, Master's Graduate, College Graduate, Senior High School Graduate, Vocational Graduate, Highschool Graduate, Elementary Graduate, Preschool Graduate, NOT APPLICABLE (did not receive any formal education)
    legalGuardianHighestEducationCompleted: '',
    legalGuardianOccupationOrTitle: '',
    // Allowance, Business, Commission, Donations/Contributions and the like, Interest on Savings/Placements/Investments, Pension, Salary, NOT IN THE LIST, NOT APPLICABLE (economically inactive; unemployed or no business)
    legalGuardianSourceIncome: '',
    // NOT APPLICABLE (economically inactive; unemployed or no business), below 10k, 10k to 20k, 20k to 30k , 30k to 40k , 40k to 50k, 50k and advance
    legalGuardianGrossMonthlyIncome: '',
    legalGuardianCompanyEmployer: {
      name: '',
      address: '',
      contactNumber: ''
    },
    siblings: [
      {
        siblingLastName: '',
        siblingFirstName: '',
        siblingMiddleName: '',
        siblingextensionOrSuffix: '',
        siblingDateOfBirth: '',
        siblingAge: '',
        siblingCivilStatus: '',
        siblingHighestEducationalAttainment: '',
        siblingNameOfSchoolAttendedOrCompanyEmployer: '',
      }
    ],
    educationalInfo: [
      {
        //  PRESCHOOL, ELEMENTARY SCHOOL, JUNIOR HIGH SCHOOL,VOCATIONAL, ALS, SENIOR HIGH SCHOOL, COLLEGE/UNIVERSITY, GRADUATE: MASTER'S, GRADUATE: DOCTORATE
        curriculum: '',
        nameOfSchool: '',
        trackOrProgram: '',
        highestHonorsReceived: '',
        cityOrMunicipalityAndProvince: '',
        yearGraduated: '',
      }
    ],
    // check all that apply, Family/Friends, Career Talk/School Visit, Internet(university page, social media, etc.), others
    howYouLearnAboutLSU: '',
    // Quality Education, To boost career prospects, Offers scholarships, Proximity, Others
    reasonForChoosingLSU: '',
    shiftToAnotherProgram: {
      yesOrNo: false,
      choseProgram: ''
    },
    choiceTrackProgram1: '',
    choiceTrackProgram2: '',
    choiceTrackProgram3: '',
    foreignStatusInfoVisaStatus: '',
    foreignLastDayOfAuthorizedStay: '',
    foreignAgentName: '',
    foreignPassportNumber: '',
    foreignPlaceIssued: '',
    foreignDateIssued: '',
    foreignDateOfExpiry: '',
    foreignACRICARDDateIssued: '',
    foreignACRICARDDateOfExpiry: '',
    foreignCRTSDateIssued: '',
    foreignCRTSDateOfExpiry: '',
    foreignSSPDateIssued: '',
    foreignSSPDateOfExpiry: '',
    healthCheckInSurveryInPresentTime: {
      physicalHealth: '',
      mentalHealth: ''
    },
    mentalHealthSupport: '',
    SAOPersonnel: {
      remarks: '',
      checkedVerifiedBy: '',
      dateVerified: '',
    },
    promissoryNoteApprovalStatus: {
      status: '',
      remarks: ''
    },
    documents: '',
    createdBy: '',
  }
</script>

<template>
  <div class="bg-gray-50">
    <Header />
    <div class="">
      <div class="">
        <div class="">
          <div class="pt-10 w-full bg-green-900">
            <h1
              class="font-bold uppercase text-white lg:text-2xl text-lg w-11/12 mx-auto pt-14 pb-5"
            >
              Admissions
            </h1>
          </div>
          <div class="pt-2.5 pb-3 shadow-lg">
            <ul class="flex lasalle-green-text capitalize w-11/12 mx-auto text-xs">
              <li>
                <a href="/" class="mr-1"> Home </a>
              </li>
              <li>
                <i class="fas fa-caret-right mr-1"></i>
                <a href="/enrollment" class="mr-1"> Enrollment </a>
              </li>
              <li>
                <i class="fas fa-caret-right mr-1"></i>
                <a href="/admissions" class="mr-1"> Admissions </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <!-- Learner Management System -->
      <!-- <div>
        <h3>CERTIFICATION</h3>
        <ul>
          By clicking the button below, I certify that:

          <li>all information in this application is factually true and completely presented; </li>
          <li> I am the person submitting this form;</li>
          <li> falsification or withholding of information requested in this form automatically nullify my application and/or subject me for dismissal, even if already admitted;</li>
          <li> credentials and information filed/submitted in support of this application become the property of La Salle University - Ozamiz City. Including future collection of photographic, video, electronic images, and/or voice in class composites, yearbooks, commencement programs, brochures, websites, promotional media, and other marketing and communication materials;</li>
          <li>documents submitted are not returnable to the applicant/student, and;</li>
          <li>collection and use of this data, which may include personal information and sensitive personal information, shall be in accordance with the Data Privacy Act of 2012.</li>
        </ul>
      </div> -->
      <div>
        <p>Checking of the box below shall be interpreted as constituting a signature of certification of the above-mentioned.</p>
        <input type="checkbox" /><label>I CERTIFY</label>
      </div>
      <div class="w-11/12 mx-auto items-center flex lg:py-10 pt-5 mb-5 shadow-lg">
        <div class="mx-auto lg:mt-0 lg:flex lg:gap-20">
          
         </div>
        </div>
      </div>
    <Footer />
  </div>
</template>

<style scoped>
.bg-green-10 {
  background: #003613;
}
</style>
