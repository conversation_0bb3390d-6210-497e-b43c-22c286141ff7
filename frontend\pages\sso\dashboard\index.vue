<script setup>
  import {
    onMounted,
    ref
  } from "vue";
  import {
    useUserStore
  } from "@/stores/user";
//   import moment from "moment";
//   import VueDatePicker from "@vuepic/vue-datepicker";
//   import "../css/main.css";
  const router = useRouter();
  const userStore = useUserStore();
  import _ from "lodash";
  const endpoint = ref(userStore.mainDevServer);
  const listItems = ref(0);
  let deleteIDItem = ref();
  let tableDisplay = ref(true);
  let toggleSideBarMenu = ref(false);
  let toggleConfirmDelete = ref(false);
  const confirmModal = ref(false);
  const route = useRoute();

  const displayUpdateForm = ref(false);

  onMounted(() => {
    setTimeout(async () => {
      if (userStore.user.isAuthenticated && (
        userStore.user.email === "<EMAIL>")) {
        // listItems.value = await $fetch(endpoint.value + "/api/drs/list").catch((error) => error.data) || 0;
        router.push("/sso/dashboard");
      } else {
        router.push("/unauthorized");
      }
    }, 5000)
  });

  const toggleDeleteBtn = (id) => {
    toggleConfirmDelete.value = !toggleConfirmDelete.value;
    deleteIDItem.value = id;
  };
  const deleteItem = async () => {
    await $fetch(endpoint.value + "/api/drs/delete/" + deleteIDItem.value, {
      method: "DELETE",
      headers: {
        Authorization: userStore.user.token,
        "Content-Type": "application/json",
      },
    }).then(async (response) => {
      console.log("response", response);
      listItems.value = await $fetch(endpoint.value + "/api/drs/list").catch((error) => error.data) || 0;
      toggleConfirmDelete.value = !toggleConfirmDelete.value;
    })
  }
  const logOut = () => {
    userStore.removeToken();
    router.push("/sso/login");
  };

  const goToEdit = async (id) => {
    // router.push("/sso/dashboard/edit/" + id);
    listItems.value = await $fetch(endpoint.value + "/api/drs/" + id + "/").catch((error) => error.data)
    displayUpdateForm.value = true;
  }

  const updateData = async (id) => {
    console.log(id)
    await $fetch(endpoint.value + "/api/drs/edit/" + id + "/", {
      method: "PUT",
      body: listItems.value,
    }).then((response) => {
      console.log("response", response);
      console.log(listItems.value)
    })
  }
</script>
<template>
  <div>
    <div class="lg:flex min-h-screen">
      <div class="pb-3 lg:w-3/12 bg-gray-100 z-10 w-full lg:block lg:static absolute 
      lg:min-h-auto min-h-[700px] lg:h-auto h-screen" 
        :class="toggleSideBarMenu ? '' : 'hidden'">
        <div class="flex items-center justify-center  text-white bg-green-900 lg:py-[16px] py-[8px]">
          <div class="flex items-center w-full px-2">
            <i class="fa fa-user mx-2" aria-hidden="true"></i>
            <h1 class="text-sm">
              {{ userStore.user.email }}
            </h1>
          </div>
          <div @click="toggleSideBarMenu = !toggleSideBarMenu" class="w-10 px-1.5 lg:hidden flex">
            <i class="fa text-3xl text-white" :class="toggleSideBarMenu ? 'fa-caret-left' : 'fa-bars'" aria-hidden="true"></i>
          </div>
        </div>
        <div class="">
          <div class="w-fit mx-auto mt-5 mb-3">
            <img src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/logos/LSU_Seal.PNG" class="lg:w-24 w-20 mx-auto" />
          </div>
          <div class="text-center">
            <h1 class="font-bold text-green-800 text-2xl">Dashboard</h1>
          </div>
          <div class="mx-auto mt-10 mb-5 grid grid-cols-1 lg:tracking-tight font-bold">
            <a href="/sso/dashboard" class="text-xs mx-auto mb-2 w-full uppercase whitespace-nowrap px-5 py-1 text-left text-black hover:bg-black hover:text-white">
              <i class="fa fa-list mr-3" aria-hidden="true"></i> All Request Lists </a>
            <a href="/" class="text-xs mx-auto mb-2 w-full uppercase whitespace-nowrap px-5 py-1 text-left
            hover:bg-black hover:text-white ">
              <i class="fa fa-globe mr-3" aria-hidden="true"></i> LSU HOME PAGE </a>
          </div>
        </div>
      </div>
      <div class="w-full">
        <div class="bg-green-800">
          <div class="flex mx-auto justify-between py-2 px-3.5">
            <div @click="toggleSideBarMenu = !toggleSideBarMenu" class="w-10 px-1.5">
              <i class="fa text-3xl text-white" :class="toggleSideBarMenu ? 'fa-caret-left' : 'fa-bars'" aria-hidden="true"></i>
            </div>
            <button @click="logOut" class="flex hover:font-bold pt-1">
              <i class="fa fa-sign-out text-white text-xl"></i>
              <h1 class="text-xs text-white p-1.5">Log Out</h1>
            </button>
          </div>
        </div>





        <div v-if="!displayUpdateForm">
          <div class="w-full lg:p-5 px-2 py-2">
            <div v-show="tableDisplay">
              <div class="w-full shadow bg-gray-100 text-green-900 font-bold px-2 text-center mb-3 py-2 text-xs uppercase"> All Request Lists </div>
              <div class="relative">
                <div class="appointment-lists mx-auto text-xs">
                  <div class="">
                    <div class="lg:flex hidden bg-green-800 text-white pb-2 pt-2.5 gap-x-7 px-3">
                      <div class="w-full mx-auto text-center font-bold text-sm"> Office </div>
                      <div class="w-full mx-auto text-center font-bold text-sm"> Status </div>
                      <div class="w-full mx-auto text-center font-bold text-sm"> Document Title </div>
                      <div class="w-full mx-auto text-center font-bold text-sm"> Action </div>
                    </div>
                    <div class="lg:border text-xs lg:rounded-none rounded-lg lg:shadow" v-if="listItems.length > 0">
                      <div class="lg:flex justify-evenly text-left items-center h-auto px-3 gap-x-7 
                            border-gray-200 lg:py-1 py-3 lg:mb-0 mb-5 lg:border-y border 
                            lg:shadow-none shadow-md lg:bg-gray-50 bg-gray-100" v-for="(b, i) in _.orderBy(listItems, 'id', 'asc')" :key="i">
                        <div class="w-full mx-auto text-center font-bold text-sm"> {{ b.originating_office }} </div>
                        <div class="w-full mx-auto text-center font-bold text-sm"> {{ b.status }} </div>
                        <div class="w-full mx-auto text-center font-bold text-sm">{{ b.document_title }} </div>
                        <div class="w-full mx-auto text-center font-bold text-sm">
                          <div>
                            <div class="text-gray-800 hover:bg-white bg-yellow-500 border border-yellow-500 px-3 py-1 rounded-md w-fit mx-auto cursor-pointer" @click="goToEdit(b.id)"> View Details </div>
                          </div>
                        </div>
                      </div>
                      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[#ffffff59] w-full h-screen flex items-center" v-show="confirmModal">
                        <div class="w-fit mx-auto flex items-center shadow-2xl border-2 rounded-lg py-2 bg-white">
                          <div class="flex items-center">
                            <div class="font-bold pl-5 pr-10">Confirm Remarks: </div>
                            <div class="flex gap-10 mr-10">
                              <span class="px-3 uppercase py-1 rounded-lg bg-green-900 border hover:border-green-900 
                              hover:bg-white hover:text-green-900 text-white font-bold cursor-pointer" @click="btnConfirm">yes</span>
                              <span class="px-3 uppercase py-1 rounded-lg bg-red-700 border hover:border-red-700 
                              hover:bg-white hover:text-green-900 text-white font-bold cursor-pointer" @click="btnCloseModal">no</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="px-5 rounded-lg bg-white shadow-2xl absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" v-show="toggleConfirmDelete">
                        <div class="my-6">Are you sure you want to Delete?</div>
                        <div class="flex gap-5 mx-auto w-fit my-5">
                          <span class="bg-green-900 text-white px-3 py-1 rounded-lg cursor-pointer" @click="deleteItem">Yes</span>
                          <span class="bg-red-900 text-white px-3 py-1 rounded-lg cursor-pointer" @click="toggleDeleteBtn">Cancel</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="listItems.length === 0" class="italic text-gray-400 text-center p-1 h-screen"> Please Wait ... </div>
                  <!-- <div v-else class="px-2 py-2 text-gray-400 text-center  h-screen" 
                  :class="listItems.length > 0 ? 'hidden' : ''"> No Results Found! </div> -->
                </div>
                <!-- <div class="mx-auto w-4/12 text-center mt-5 text-gray-400 mb-3"><div class="inline-flex gap-5"><button
                      class="bg-green-800 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-l"
                      @click="prevBtn">
                      Prev
                    </button><button class="bg-green-800 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-l" 
                      @click="currentBtn">
                      1
                    </button><button class="bg-green-800 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-l">
                      2
                    </button><button class="bg-green-800 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-l">
                      3
                    </button><button
                      class="bg-green-800 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-r"
                    @click="nextBtn">
                      Next
                    </button></div></div> -->
              </div>
            </div>
          </div>
        </div>
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
      
       <div v-if="displayUpdateForm">
          <div class="w-full lg:p-5 px-2 py-2">
            <div>
              <form v-on:submit.prevent="updateData(listItems.id)">
                <div class="border-t-2 border-green-700 shadow-lg mx-auto ">
                  <div class="">
                    <h2 class="lg:text-base text-xs px-10 uppercase py-2 font-bold bg-green-900 text-white text-center"> Document Review Sheet Form <span class="font-light text-xs bg-green-900 text-white block">
                        {{ listItems.document_code }}</span>
                    </h2>
                    <div class="w-fit mx-auto text-xs mt-4 px-4 font-montserrat tracking-tight"> DRS No. <span class="border-b px-1">{{ listItems.tracking_id }}</span>
                    </div>
                    <div class="px-2 pt-3 pb-2 gap-3">
                      <div class="w-full lg:mb-0 mb-5">
                        <div class="w-full gap-3">
                          <div class="gap-3 w-full">
                            <div class="gap-3 lg:mb-2 w-full mx-auto border shadow py-5 px-5">
                              <div class="lg:gap-x-2 gap-x-1 w-full">
                                <div class="flex items-center w-full mb-2">
                                  <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold whitespace-nowrap w-4/12">
                                    <div class="w-fit ml-auto pr-5"> Originating Office <span class="text-red-600 font-normal text-sm">*</span>
                                    </div>
                                  </label>
                                  <input type="text" class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg 
                            rounded-sm lg:h-9 h-8 text-xs" 
                            placeholder="Originating Office" 
                            v-model="listItems.originating_office" />
                                </div>
                                <div class="flex items-center w-full mb-2">
                                  <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold whitespace-nowrap w-4/12">
                                    <div class="w-fit ml-auto pr-5"> Email <span class="text-red-600 font-normal text-sm">*</span>
                                    </div>
                                  </label>
                                  <div class="w-full">
                                    <input type="email" class="px-2 w-6/12 border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg 
                            rounded-sm lg:h-9 h-8 text-xs" placeholder="Email" v-model="listItems.originating_email" disabled />
                                  </div>
                                </div>
                                <div class="flex items-center w-full mb-2">
                                  <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold whitespace-nowrap w-4/12">
                                    <div class="w-fit ml-auto pr-5"> Document Title <span class="text-red-600 font-normal text-sm">*</span>
                                    </div>
                                  </label>
                                  <input type="text" class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg 
                            rounded-sm lg:h-9 h-8 text-xs" placeholder="Document Title" v-model="listItems.document_title" disabled />
                                </div>
                                <div class="flex items-center w-full mb-2">
                                  <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold whitespace-nowrap w-4/12">
                                    <div class="w-fit ml-auto pr-5"> Document Type <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                                    </div>
                                  </label>
                                  <div class="w-full">
                                    <div class="lg:flex gap-x-3 w-fit px-3 lg:px-0 lg:shadow lg:pt-[2px] pt-3 bg-white border-green-700 border-b-2">
                                      <div class="lg:shadow-lg px-2 lg:pt-[8px] lg:pb-[9px] pb-2.5    text-black text-xs items-center flex ">
                                        <input type="radio" value="Manual" disabled v-model="listItems.document_type" class="mr-1" id="Manual" />
                                        <label class="ml-2" for="Manual">
                                          <span class=" font-bold mr-1">Manual</span>
                                        </label>
                                      </div>
                                      <div class="lg:shadow-lg lg:mt-0 mt-2 px-2 lg:pt-[8px] lg:pb-[9px] pb-2.5    text-black text-xs items-center flex ">
                                        <input type="radio" value="Procedure" disabled v-model="listItems.document_type" class="mr-1" id="Procedure" />
                                        <label class="ml-2" for="Procedure">
                                          <span class=" font-bold mr-1">Procedure</span>
                                        </label>
                                      </div>
                                      <div class="lg:shadow-lg lg:mt-0 mt-2 px-2 lg:pt-[8px] lg:pb-[9px] pb-2.5    text-black text-xs items-center flex ">
                                        <input type="radio" value="Policy" disabled v-model="listItems.document_type" class="mr-1" id="Policy" />
                                        <label class="ml-2" for="Policy">
                                          <span class=" font-bold mr-1">Policy</span>
                                        </label>
                                      </div>
                                      <div class="lg:shadow-lg lg:mt-0 mt-2 px-2 lg:pt-[8px] lg:pb-[9px] pb-2.5   text-black text-xs items-center flex ">
                                        <input type="radio" value="Form or Template" disabled v-model="listItems.document_type" class="mr-1" id="FormTemplate" />
                                        <label class="ml-2" for="FormTemplate">
                                          <span class=" font-bold mr-1">Form or Template</span>
                                        </label>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="flex items-center w-full mb-2">
                                  <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold whitespace-nowrap w-4/12">
                                    <div class="w-fit ml-auto pr-5"> Status <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                                    </div>
                                  </label>
                                  <div class="w-full">
                                    <div class="lg:flex gap-x-3 w-fit px-3 lg:px-0 lg:shadow lg:pt-[2px] pt-3 bg-white border-green-700 border-b-2">
                                      <div class="lg:shadow-lg px-2 lg:pt-[8px] lg:pb-[9px] pb-2.5    text-black text-xs items-center flex ">
                                        <input type="radio" value="New" v-model="listItems.status" @change="changeStatus()" class="mr-1" id="New" ref="fileInput" disabled />
                                        <label class="ml-2" for="New">
                                          <span class=" font-bold mr-1">New</span>
                                        </label>
                                      </div>
                                      <div class="flex items-center text-xs whitespace-nowrap lg:shadow-lg lg:mt-0 mt-2 px-2">
                                        <label class="ml-2 flex item w-full text-black text-xs items-center" for="ForRevision">
                                          <span class="mr-2">
                                            <input type="radio" value="For Revision" v-model="listItems.status" @change="changeStatus()" class="" id="ForRevision" disabled />
                                          </span>
                                          <span class="font-bold mr-1">For Revision</span>
                                          <span :class="forRevisionInput ? '': 'hidden'" class="font-bold mr-2">No. </span>
                                          <input :class="forRevisionInput ? '': 'hidden'" type="text" class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg py-1 rounded-sm text-xs" placeholder="Revision Number" v-model="listItems.revision_number" ref="fileInput" disabled />
                                        </label>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="flex items-center w-full mb-2">
                                  <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold whitespace-nowrap w-4/12">
                                    <div class="w-fit ml-auto pr-5"> Date <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                                    </div>
                                  </label>
                                  <div class="w-full">
                                    <div class="border-b-2 border-green-700 w-fit shadow-lg">
                                     
                                    </div>
                                  </div>
                                </div>
                                <div class="flex items-center w-full mb-2">
                                  <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold whitespace-nowrap w-4/12">
                                    <div class="w-fit ml-auto pr-5"> Purpose <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                                    </div>
                                  </label>

                                  <div class="w-full"> 
                                    <input type="text" class="px-2 lg:w-6/12 border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg 
                                    rounded-sm lg:h-9 h-8 text-xs" placeholder="Purpose" v-model="listItems.purpose" disabled />

                                  </div>
                                 
                                </div>
                                <div class="flex items-center w-full mb-2">
                                  <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold whitespace-nowrap w-4/12">
                                    <div class="w-fit ml-auto pr-5"> Reference Document <span class="text-red-600 font-normal text-sm">*</span>
                                    </div>
                                  </label>
                                  <!-- <input type="url" class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs" 
                                  placeholder="Google Drink Link" v-model="listItems.document_attachment" required disabled/> -->
                                  <div class="w-full"> 
                                    <a :href="listItems.document_attachment" class="flex px-2 py-2 lg:w-fit w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm h-8 text-xs" > 
                                    <img src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/Google_Drive.png" 
                                    class="h-5 w-5 mr-3" /> {{listItems.document_attachment}}
                                  </a>
                                  </div>
                                
                                </div>
                              </div>
                            </div>
                            <div class="w-fit mx-auto my-5">
                              <div class="text-center flex">
                                <input type="text" class="px-0.5 uppercase w-full 
                        rounded-sm lg:h-9 h-8 text-xs text-right" placeholder="First Name" v-model="listItems.originating_firstname" disabled />
                                <input type="text" class="px-0.5 uppercase w-full 
                        rounded-sm lg:h-9 h-8 text-xs" placeholder="Last Name" v-model="listItems.originating_lastname" disabled />
                              </div>
                              <div class="border-t-2 border-black text-center">Originator’s Printed Name</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="px-2">
                      <div class="flex lg:gap-x-2 gap-x-1 w-full shadow-xl border px-3 py-3 my-5">
                        <div class="w-full mb-2 block">
                          <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold"> Reviewed By <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                          </label>
                          <div class="w-fit mx-auto ">
                            <div class="text-center flex">
                              <input type="text" class="px-2 w-full 
                        rounded-sm h-fit py-1 text-sm text-center" placeholder="" v-model="listItems.reviewed_by_name" 
                         :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"
                        />
                            </div>
                            <div class="border-t-2 border-black text-center text-xs">{{ listItems.reviewed_by_designation }}</div>
                          </div>
                        </div>
                        <div class="w-full mb-2 block">
                          <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold"> Action <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                          </label>
                          <div class="lg:flex gap-x-3 w-fit px-3 lg:px-0 lg:shadow lg:pt-[2px] pt-3 bg-white border-green-700 border-b-2">
                            <div class="lg:shadow-lg px-2 lg:pt-[8px] lg:pb-[9px] pb-2.5  w-full  text-black text-xs items-center flex ">
                              <input type="radio" value="Approved" 
                              v-model="listItems.type_of_access" 
                              @change="needSupportingDocs()" class="mr-1" id="Approved" 
                               :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"/>
                              <label class="ml-2" for="Approved">
                                <span class=" font-bold mr-1">Approved</span>
                              </label>
                            </div>
                            <div class="lg:shadow-lg lg:mt-0 mt-2 px-2 lg:pt-[8px] lg:pb-[9px] pb-2.5  w-full  text-black text-xs items-center flex ">
                              <input type="radio" value="Disapproved" v-model="listItems.type_of_access" @change="needSupportingDocs()" class="mr-1" id="Disapproved" 
                               :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"
                              />
                              <label class="ml-2" for="Disapproved">
                                <span class=" font-bold mr-1">Disapproved</span>
                              </label>
                            </div>
                            <div class="lg:shadow-lg lg:mt-0 mt-2 px-2 lg:pt-[8px] lg:pb-[9px] pb-2.5  w-full  text-black text-xs items-center flex ">
                              <input type="radio" value="Conditional" v-model="listItems.type_of_access" class="mr-1" id="Conditional" @change="needSupportingDocs()" 
                               :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"
                              />
                              <label class="ml-2" for="Conditional">
                                <span class=" font-bold mr-1">Conditional</span>
                              </label>
                            </div>
                          </div>
                        </div>
                        <div class="w-full mb-2 block">
                          <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold"> Comments or Remarks <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                          </label>
                          <textarea type="text" class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg py-2
                            rounded-sm lg:h-9 h-8 text-xs" placeholder="Comments or Remarks" v-model="listItems.reviewed_by_remarks" required
                            :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"
                            ></textarea>
                        </div>
                      </div>
                      <div class="flex lg:gap-x-2 gap-x-1 w-full shadow-xl border px-3 py-3 my-5">
                        <div class="w-full mb-2 block">
                          <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold"> Approved By <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                          </label>
                          <div class="w-fit mx-auto ">
                            <div class="text-center flex">
                              <input type="text" class="px-2 w-full 
                        rounded-sm h-fit py-1 text-sm text-center" placeholder="" v-model="listItems.approved_by_name" 
                         :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>' || userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"
                        />
                            </div>
                            <div class="border-t-2 border-black text-center text-xs">{{ listItems.approved_by_designation }}</div>
                          </div>
                        </div>
                        <div class="w-full mb-2 block">
                          <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold"> Action <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                          </label>
                          <div class="lg:flex gap-x-3 w-fit px-3 lg:px-0 lg:shadow lg:pt-[2px] pt-3 bg-white border-green-700 border-b-2">
                            <div class="lg:shadow-lg px-2 lg:pt-[8px] lg:pb-[9px] pb-2.5  w-full  text-black text-xs items-center flex ">
                              <input type="radio" value="Approved" v-model="listItems.type_of_access" @change="needSupportingDocs()" class="mr-1" id="Approved" 
                              :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>' || userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"
                              />
                              <label class="ml-2" for="Approved">
                                <span class=" font-bold mr-1">Approved</span>
                              </label>
                            </div>
                            <div class="lg:shadow-lg lg:mt-0 mt-2 px-2 lg:pt-[8px] lg:pb-[9px] pb-2.5  w-full  text-black text-xs items-center flex ">
                              <input type="radio" value="Disapproved" v-model="listItems.type_of_access" @change="needSupportingDocs()" class="mr-1" id="Disapproved" 
                              :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>' || userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"
                              />
                              <label class="ml-2" for="Disapproved">
                                <span class=" font-bold mr-1">Disapproved</span>
                              </label>
                            </div>
                            <div class="lg:shadow-lg lg:mt-0 mt-2 px-2 lg:pt-[8px] lg:pb-[9px] pb-2.5  w-full  text-black text-xs items-center flex ">
                              <input type="radio" value="Conditional" v-model="listItems.type_of_access" class="mr-1" id="Conditional" @change="needSupportingDocs()" 
                              :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>' || userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"
                              />
                              <label class="ml-2" for="Conditional">
                                <span class=" font-bold mr-1">Conditional</span>
                              </label>
                            </div>
                          </div>
                        </div>
                        <div class="w-full mb-2 block">
                          <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold"> Comments or Remarks <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                          </label>
                          <textarea type="text" class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg py-2
                            rounded-sm lg:h-9 h-8 text-xs" placeholder="Comments or Remarks" v-model="listItems.approved_by_remarks" required
                            :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>' || userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"
                            ></textarea>
                        </div>
                      </div>
                      <div class="flex lg:gap-x-2 gap-x-1 w-full shadow-xl border px-3 py-3 my-5">
                        <div class="w-full">
                          <div>
                            <div class="w-full mb-2 block">
                              <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold"> Other Comment and Remarks <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                              </label>
                              <input type="text" class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg py-2
                          rounded-sm lg:h-9 h-8 text-xs" placeholder="Other Comment and Remarks" v-model="listItems.other_comments_remarks" required 
                          :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>' || userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"
                          />
                            </div>
                          </div>
                          <div class="flex gap-x-3">
                            <div class="w-full mb-2 block">
                              <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold"> Document Code <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                              </label>
                              <input type="text" class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg 
                            rounded-sm lg:h-9 h-8 text-xs" placeholder="Document Code" v-model="listItems.document_code" required 
                              :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>' || userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"
                            />
                            </div>
                            <div class="w-full mb-2 block"
                             :class="['<EMAIL>', '<EMAIL>'].includes(userStore.user.email) ? '':'hidden'"
                            >
                              <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold"> Effectivity Date <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                              </label>
                              <div class="border-b-2 border-green-700 shadow-lg" 
                             >
                               
                              </div>
                            </div>
                            <div class="w-full mb-2 block">
                              <label class="lg:text-xs text-[10px] text-gray-900 pb-2 font-bold"> Records Management Officer (RMO) <span class="text-red-600 font-normal text-sm lg:ml-1">*</span>
                              </label>
                              <input type="text" 
                              class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg 
                            rounded-sm lg:h-9 h-8 text-xs" placeholder="Records Management Officer (RMO)" v-model="listItems.approved_by_remarks" required  
                               :disabled="!(userStore.user.email?.trim().toLowerCase() === '<EMAIL>' || userStore.user.email?.trim().toLowerCase() === '<EMAIL>')"/>   
                             
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-if="pleaseFillUpAllRequiredFields" 
                    class="my-10 text-white bg-red-800 font-bold text-center py-0.5 px-5 block lg:text-sm text-xs"> All fields are required information. Otherwise, type N/A if not applicable. </div>
                    <div class="pb-5 lg:px-5 px-3 mb-5">
                      <button class="px-10 lg:rounded-lg rounded-md bg-yellow-500 text-white font-bold 
              lg:py-2 py-1.5 lg:w-fit w-full mx-auto block uppercase hover:bg-white border-2 
              border-yellow-500 hover:text-yellow-500 lg:text-sm text-xs">
                        <i class="fa fa-paper-plane mr-2" aria-hidden="true"></i> Update </button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      
      </div>
    </div>
    <div class="static bottom-0 w-full">
      <DashboardFooter />
    </div>
  </div>
</template>
<style scoped>
  input[type="checkbox"] {
    color: #116f00;
  }

  input[type="checkbox"] {
    color: #116f00;
  }

  input[type="radio"] {
    margin: 3px auto auto auto;
  }

  .error {
    color: red;
  }

  input[type="file"] {
    display: none;
  }
</style>