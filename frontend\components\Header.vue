<script setup>
const menuDetails = ref(false);
</script>

<template>
  <!-- <div class="lasalle-green" :class="? 'fixed z-10':''"> -->
  <div
    class="lasalle-green fixed main-header w-full font-montserrat"
    style="box-shadow: 0 5px 12px 0 rgb(0 0 0 / 20%), 0 3px 5px 0 rgb(0 0 0 / 19%)"
  >
    <div class="flex w-11/12 mx-auto justify-between">
      <a href="/" class="lg:hover:bg-green-800 lg:hover:pr-3 -ml-3 pl-3">
        <img
          src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/logos/lsu-w-h.png"
          class="w-48 lg:w-60 lg:my-2.5 my-2"
        />
      </a>
      <div class="lg:block hidden">
        <ul class="flex uppercase font-semibold gap-1">
          <li class="text-xs relative nav">
            <a href="#" class="text-white hover:text-green-900 hover:bg-white rounded-lg block
            h-fit hover:pt-4 hover:pb-3 hover:mt-3 hover:mb-1 px-5 mt-7 mb-5 
            "
              >Academics <i class="fa fa-caret-down" aria-hidden="true"></i
            ></a>
            <ul
              class="text-xs lasalle-green text-white absolute text-left whitespace-nowrap z-10 rounded-md py-2 px-3 subnav capitalize"
            >
              <li class="hover:font-bold">
                <a href="/academics/tertiary-education"> Tertiary Education </a>
              </li>
            </ul>
          </li>
          <li class="text-xs">
            <a href="/administration" class="text-white hover:text-green-900 hover:bg-white rounded-lg block 
            h-fit hover:pt-4 hover:pb-3 hover:mt-3 hover:mb-1 px-5 mt-7 mb-5"
              >Administration
            </a>
          </li>
          <li class="text-xs">
            <a href="/directory" class="text-white hover:text-green-900 hover:bg-white rounded-lg block
            h-fit hover:pt-4 hover:pb-3 hover:mt-3 hover:mb-1 px-5 mt-7 mb-5">Contact </a>
          </li>
          <li class="text-xs relative nav">
            <a href="#" class="text-white hover:text-green-900 hover:bg-white rounded-lg block 
            h-fit hover:pt-4 hover:pb-3 hover:mt-3 hover:mb-1 px-5 mt-7 mb-5"
              >more <i class="fa fa-caret-down" aria-hidden="true"></i
            ></a>
            <ul
              class="text-xs lasalle-green text-white absolute text-left whitespace-nowrap z-10 rounded-md py-3 px-3 subnav capitalize"
            >
              <li class="py-1 hover:font-bold">
                <a href="/campus-pass"> Campus Pass </a>
              </li>
              <li class="py-1 hover:font-bold">
                <a href="/registrar"> Registrar </a>
              </li>
              <li class="py-1 hover:font-bold">
                <a href="/procurement"> Procurement </a>
              </li>
              <li class="py-1 hover:font-bold">
                <a href="/social-media"> Student Affairs </a>
              </li>
              <li class="py-1 hover:font-bold">
                <a href="/about"> About </a>
              </li>
              <li class="py-1 hover:font-bold">
                <a href="/hr"> Human Resource </a>
              </li>
              <li class="py-1 hover:font-bold">
                <a href="/drs"> Document Reviewer </a>
              </li>
            </ul>
          </li>
        </ul>
      </div>
      <div class="lg:hidden block" @click="menuDetails = !menuDetails">
        <i
          class="fa text-3xl text-white pt-3"
          :class="menuDetails ? 'fa-times' : 'fa-bars'"
          aria-hidden="true"
        ></i>
      </div>
    </div>
    <div
      class="font-bold w-full z-30 absolute shadow-lg bg-white pb-20 right-0 text-green-900"
      :class="menuDetails ? 'lg:hidden block min-h-screen overflow-y-scroll' : 'hidden'"
    >
      <ul class="px-5 w-full gap-10 text-left py-3">
        <li class="lg:text-xl text-base py-0.5">
          <a href="/">Home</a>
        </li>
        <li class="lg:text-xl text-base py-1.5 relative">
          <a href="#" class="pb-2"> Academics </a>
          <ul class="list-disc ml-5 lg:text-xl text-base text-left whitespace-nowrap rounded-md">
            <li class="py-0.5">
              <a href="/academics/tertiary-education"> Tertiary Education </a>
            </li>
            <li class="py-0.5">
              <a href="/class-schedules"> Class Schedules </a>
            </li>
          </ul>
        </li>
        <li class="lg:text-xl text-base">
          <a href="/administration">Administration</a>
        </li>
        <li class="lg:text-xl text-base py-1.5 relative">
          <a href="#" class="pb-5"> More </a>
          <ul class="list-disc ml-5 lg:text-xl text-base py-1 text-left whitespace-nowrap rounded-md">
            <li class="py-0.5">
              <a href="/campus-pass"> Campus Pass </a>
            </li>
            <li class="py-0.5">
              <a href="/procurement"> Procurement </a>
            </li>
            <li class="py-0.5">
              <a href="/library"> Library </a>
            </li>
            <li class="py-0.5">
              <a href="/social-media"> Student Affairs </a>
            </li>
            <li class="py-0.5">
              <a href="/registrar"> Registrar Appointment </a>
            </li>
            <li class="py-0.5">
              <a href="/data-privacy"> Data Privacy </a>
            </li>
            <li class="py-0.5">
              <a href="/downloads"> Downloads </a>
            </li>
            <li class="py-0.5">
              <a href="/about"> About </a>
            </li>
            <li class="py-0.5">
              <a href="/directory"> Contact Us </a>
            </li>
            <li class="py-0.5">
              <a href="/hr"> Human Resource </a>
            </li>
            <li class="py-0.5">
              <a href="/social-media"> Social Media </a>
            </li>
            <li class="py-0.5">
              <a href="/drs"> Document Reviewer </a>
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .nav {
    .subnav {
      display: none;
    }
    &:hover {
      color: #00ff84;
      .subnav {
        display: block;
      }
    }
  }

  .main-header {
    z-index: 50;
  }
</style>