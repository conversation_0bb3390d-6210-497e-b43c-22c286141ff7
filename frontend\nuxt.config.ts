export default defineNuxtConfig({
  experimental: { appManifest: false },
  devtools: { enabled: false },

  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: 'La Salle University - Ozamiz',
      link: [
        {
          rel: "stylesheet",
          href: "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.3.0/css/all.min.css",
        },
        {
          rel: "stylesheet",
          href: "/css/flickity.min.css",
        },
        // {
        //   rel: "stylesheet",
        //   href: "https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.6.5/flowbite.min.css",
        // },
      ],
      script: [
        {
          src: "/js/flickity.pkgd.min.js"
        },
        // {
        //   src: "https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.6.5/flowbite.min.js"
        // },
        // {
        //   src: 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js',
        //   'data-ad-client': 'ca-pub-2783005418884897',
        //   async: true
        // }
      ]
    },
  },

  css: ["~/assets/css/main.css"],

  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},
    },
  },

  googleSignIn: {
    clientId: '818409831356-g3thpiuagqs1oj6sh1bk6j1c25j2m2mk.apps.googleusercontent.com'
  },

  googleAdsense: {
    id: 'ca-pub-2783005418884897'
  },

  build: {
    transpile: ['@vuepic/vue-datepicker']
  },

  modules: [
    'nuxt-vue3-google-signin', 
    '@pinia/nuxt', 
    'nuxt-lodash', 
    '@nuxtjs/google-adsense', 
    'nuxt-gtag'
      // '@nuxt/scripts'
      // ['@nuxtjs/google-adsense', {
      //   id: 'ca-pub-2783005418884897', // replace "#" with "2112345678904791",
      //   onPageLoad: true, // this is required to be true for our ads to show in our 
      //   test: false // if we are using development env. the test variable will help us to show where your ads will appear
      // }]
  ],

  gtag: {
    id: 'G-G21JEZYE42'
  },

  // scripts: {
  //   registry: {
  //     googleAdsense: {
  //       client: 'ca-pub-2783005418884897'
  //     },
  //   },
  // },
  // routeRules: { '/': {ssr: false} }
  // "Content-Type": "multipart/form-data",
  // 'Content-Type': 'application/json',
  // 'Accept': 'application/json',
  nitro: {
    prerender: {
      failOnError: false,
    },
  },

  compatibilityDate: '2025-07-30',
});