<script setup>
</script>

<template>
  <div>
    <Header />
    <div>
      <div class="bg-gray-50">
        <div class="">
          <div class="relative">
            <img
              src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/about.jpg"
              class="align-top w-full h-auto lg:object-fill lg:block hidden"
            />
            <img
              src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/green-tones-gradient-background_23-2148374436.png"
              class="align-top w-full h-36 object-none lg:hidden block"
            />
            <div
              class="pt-10 absolute top-1/2 transform -translate-y-1/2 w-full"
            >
              <h1
                class="font-bold uppercase text-white lg:text-2xl text-lg w-11/12 mx-auto"
              >
                LIBRARY ONLINE SERVICES
              </h1>
            </div>
            <div class="pt-2.5 pb-3 shadow-lg">
              <ul
                class="lg:flex lasalle-green-text capitalize w-11/12 mx-auto text-xs"
              >
                <li>
                  <a href="/" class="mr-1"> Home </a>
                </li>
                <li>
                  <i class="fas fa-caret-right mr-1"></i>
                  <a href="/library" class="mr-1">
                    Libraries and Media Centers
                  </a>
                </li>
                <li>
                  <i class="fas fa-caret-right mr-1"></i>
                  <a href="#" class="mr-1"> Online Library Services </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div class="mx-auto pt-10">
        <div class="lg:w-6/12 w-11/12 px-10 mx-auto">
          <h1 class="text-center lasalle-green-text text-lg mt-5 font-bold">
            Online Library Services
          </h1>

          <div class="lg:flex mb-10 lg:text-left md:text-center mt-10">
            <div class="lg:mr-10 lg:ml-0 mx-auto w-fit">
              <img
                src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/LMCImages/Picture5.png"
                class="w-44 h-44"
              />
            </div>
            <div class="flex items-center">
              <div>
                <a href="/library/myLibraryAccount" class="block"
                  >My Library Account</a
                >
                <a href="/library/howToCreateLibraryAccount" class="block"
                  >How to Create a MyLibrary Account?</a
                >
                <a href="/library/howToManageLibraryAccount" class="block"
                  >How to Manage MyLibrary Account?</a
                >
              </div>
            </div>
          </div>
          <div class="lg:flex mb-10 lg:text-left md:text-center">
            <div class="lg:mr-10 lg:ml-0 mx-auto w-fit">
              <img
                src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/LMCImages/Picture1.png"
                class="w-44 h-44"
              />
            </div>
            <div class="flex items-center">
              <div>
                <a href="/library/vcas" class="block"
                  >Virtual Circulation Assistance (VICA) Services</a
                >
                <a href="/library/wgbbubt" class="block"
                  >What are the Guidelines in Borrowing of Books Using Book
                  Thru?</a
                >
                <a href="/library/bookUsingBookDrop" class="block"
                  >What are the Guidelines in Returning of Book Using Book
                  Drop?</a
                >
              </div>
            </div>
          </div>
          <div class="lg:flex mb-10 lg:text-left md:text-center">
            <div class="lg:mr-10 lg:ml-0 mx-auto w-fit">
              <img
                src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/LMCImages/Picture14.png"
                class="w-44 h-44"
              />
            </div>
            <div class="flex items-center">
              <div>
                <a href="/library/electronicResourcesServices" class="block"
                  >Electronic Resources Services</a
                >
                <a href="/library/subrscribedElectronicResources" class="block"
                  >How to Access Subscribed Electronic Resources?</a
                >
                <a href="/library/freeElectronicResources" class="block"
                  >How to Access Free Electronic Resources?</a
                >
              </div>
            </div>
          </div>
          <div class="lg:flex mb-10 lg:text-left md:text-center">
            <div class="lg:mr-10 lg:ml-0 mx-auto w-fit">
              <img
                src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/LMCImages/Picture21.png"
                class="w-44 h-44"
              />
            </div>
            <div class="flex items-center">
              <div>
                <a href="/library/libraryWebServices" class="block"
                  >Library Web Services</a
                >
                <a href="/library" class="block"
                  >How to Access the Library Webpage?</a
                >
                <a href="/library/availableLibraryResources" class="block"
                  >How to Access Available Library Resources?</a
                >
              </div>
            </div>
          </div>
          <div class="lg:flex mb-10 lg:text-left md:text-center">
            <div class="lg:mr-10 lg:ml-0 mx-auto w-fit">
              <img
                src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/LMCImages/Picture3.png"
                class="w-44 h-44"
              />
            </div>
            <div class="flex items-center">
              <div>
                <a href="/library/onlineLibraryInstruction" class="block"
                  >Online Library Instruction</a
                >
                <a href="/library/availInformationLiteracySession" class="block"
                  >How to Avail Information Literacy (IL) Session?</a
                >
              </div>
            </div>
          </div>
          <div class="lg:flex mb-10 lg:text-left md:text-center">
            <div class="lg:mr-10 lg:ml-0 mx-auto w-fit">
              <img
                src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/LMCImages/Picture19.png"
                class="w-44 h-44"
              />
            </div>
            <div class="flex items-center">
              <div>
                <a href="/library/librarianHelpOnline" class="block"
                  >Librarian Help Online</a
                >
                <a href="/library/howToContactLibrarianOnline" class="block"
                  >How to Contact the Librarian Online?</a
                >
              </div>
            </div>
          </div>
          <div
            class="text-center text-sm italic font-semibold text-gray-900 mb-5"
          >
            <h1 class="text-green-600">"When in doubt, go to the library."</h1>
            <span>- J.K. Rowling</span>
          </div>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>
  
<style scoped>
.sub-header {
  background: url("https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/LMC/LMCSpaceBanner.png");
  background-position: center;
  background-size: 100% 100%;
}
@media only screen and (max-width: 1023px) {
  .sub-header {
    background: #087830;
  }
}
@media only screen and (max-width: 2560px) {
  .sub-header {
    height: 170px;
  }
}
@media only screen and (max-width: 1440px) {
  .sub-header {
    height: 170px;
  }
}
@media only screen and (max-width: 1024px) {
  .sub-header {
    height: 170px;
  }
}
@media only screen and (max-width: 768px) {
  .sub-header {
    height: 170px;
  }
}
@media only screen and (max-width: 425px) {
  .sub-header {
    height: 170px;
  }
}
@media only screen and (max-width: 375px) {
  .sub-header {
    height: 170px;
  }
}
@media only screen and (max-width: 320px) {
  .sub-header {
    height: 170px;
  }
}
</style>