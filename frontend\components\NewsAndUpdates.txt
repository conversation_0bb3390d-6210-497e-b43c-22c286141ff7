<script setup>
const newsupdates = [
  {
    image:
      "https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/newsupdates/n1.jpg",
    link:
      "https://www.facebook.com/lsu.edu.ph/posts/pfbid02uqmtVSEBV2nu6qXkMcMJva1DbH5c3XXyvgu1qRZCRysNf2pFehnt3QPT2iGwUDzCl",
  },
  {
    image:
      "https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/newsupdates/n2.png",
    link:
      "https://www.facebook.com/lsu.edu.ph/posts/pfbid0Cyv76mNwJA8DnyeuMnA45aQ6ahqr4vEYY3WfUvuSBgchJoYmkGmNVWaxoMvPUiTpl",
  },
  {
    image:
      "https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/newsupdates/n3.png",
    link:
      "https://www.facebook.com/lsu.edu.ph/posts/pfbid02maZUk8T7oLcsao1y5VF7ThDGKYiCfgDLhVFsUqhVTq8UCCw5acJEtJP1a87KQkdwl",
  },
];
</script>

<template>
  <div class="">
    <!-- <div class="bg-green-700">
      <div class="lg:pt-24 pt-5 lg:pb-20">
        <div class="w-11/12 mx-auto">
          <div
            class="mx-auto w-11/12 text-center font-bold lg:text-3xl text-lg uppercase text-white lg:mb-10"
          >
            News and Updates
          </div>
          <div
            class="lg:grid grid-cols-3 text-center mx-auto lg:py-10 pt-4 pb-2 gap-14 list-none"
          >
            <div
              class="hover:shadow-2xl hover:border hover:border-white w-full lg:h-[350px] h-[200px] relative lg:mb-0 mb-3 cursor-pointer shadow-4xl"
              v-for="(nu, i) in newsupdates"
              :key="i"
            >
              <NuxtLink :to="nu.link">
             
                <img :src="nu.image" class="object-cover h-full w-full z-10 absolute" />
                <button
                  class="text-left text-xs text-white w-full bg-black opacity-80 px-3 h-20 absolute bottom-0 left-1/2 transform -translate-x-1/2 z-20"
                >
                  
                  <div class="-mt-3">
                    <h1 class="text-sm font-bold">LSU Welcomes Sister Ann Carbon MSC</h1>
                    <h3 class="line-clamp-2 text-xs">
                      Welcome to La Salle University, Sister Anne Carbon MSC,
                      Congregational Leader of the Missionary Sisters of Saint Columban.
                    </h3>
                  </div>
                </button>
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div> -->

    <!-- <i class="fa fa-angle-down text-5xl" aria-hidden="true"></i> -->
    <!-- <div class="absolute w-full h-80" :style="{backgroundImage: 'url('+(nu.image)+')' , opacity: '0.2'}"></div>
                <div class="absolute w-full h-80 opacity-60"></div> -->
    <!-- <div
      class="w-fit lasalle-green-text rounded-md mx-auto text-center text-xl text-white font-bold p-3"
    >
      Higher Education
    </div> -->
    <div class="bg-green-800 w-full h-[20px]">
      <ul class="">
        <li></li>
      </ul>
    </div>


    <div class="">
      <div class="mx-auto bg-white w-full lg:flex relative lg:gap-10">
        <div class="2xl:w-7/12 xl:w-9/12 overflow-hidden flex items-center"><img
            src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/lsuOPR.jpg"
            class="xl:w-auto lg:w-full shadow-2xl lg:max-h-[888px] xl:max-h-[800px]"></div>
        <div
          class="2xl:w-4/12 xl:w-3/12 lg:w-3/12 text-left flex items-center lg:bg-none lg:bg-transparent bg-black lg:opacity-100 opacity-60 lg:mt-0 -mt-[50px] lg:px-0 px-5">
          <a href="https://www.facebook.com/lsuadvacement" rel="noopener noreferrer"
            class="lg:block flex lg:w-10/12 w-full lg:mx-auto lg:py-10 text-white justify-between"><img
              src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/logos/lsu-corporate-logo-green.png"
              class="2xl:w-[200px] xl:w-[180px] lg:w-[120px] mb-10 lg:static absolute top-2 left-2 w-24 z-10">
            <h1
              class="lg:block flex 2xl:text-6xl xl:text-4xl lg:text-2xl 2xl:leading-[70px] leading-[50px] italic font-semibold lg:text-green-700 text-white text-left">
              <span class="lg:block flex">Advancement&nbsp;</span><span>and Linkages</span>
            </h1><button
              class="cursor-pointer text-left text-xs uppercase lg:bg-green-700 lg:hover:bg-white lg:border lg:hover:border-green-800 lh:hover:text-green-800 lg:hover:text-green-800 text-white lg:px-7 md:px-3 xl:py-3.5 md:py-1.5 block w-fit lg:mt-28"><span
                class="lg:block hidden font-bold"> learn more</span><span class="lg:hidden block"><i
                  class="fa fa-angle-double-right text-xl"></i></span></button>
          </a>
        </div>
      </div>
    </div>

    <!-- <div
      class="w-fit lasalle-green-text rounded-md mx-auto text-center text-xl text-white font-bold p-3"
    >
      Basic Education
    </div>
    <div
      class="grid grid-cols-3 text-center w-11/12 mx-auto py-5 gap-5 list-none"
    >
      <li class="border w-full h-44 bg-green-600">
        <img src="" class="" />
        <p class="text-center font-bold uppercase text-white">
          APPLICATION FOR ADMISSION
        </p>
      </li>
    </div> -->
    <div class="lg:bg-green-700">
      <div class="mx-auto lg:flex items-center gap-5">
        <div class="lg:w-11/12 flex items-center lg:order-2 order-1"><img
            src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/lsuTingog.jpg"
            class="object-cover w-full lg:h-full shadow-2xl max-h-[387px]"></div>
        <div class="lg:w-5/12 text-left lg:order-1 order-2 lg:px-10">
          <a href="https://www.facebook.com/lsutingog" class="mx-auto text-white w-full">
            <div class="lg:w-24 lg:h-38 lg:mb-5">
              <img
                src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/LSUTingogLogo.jpg"
                class="xl:h-44 h-32 object-cover lg:block hidden">
            </div>
            <div class="lg:bg-transparent lg:block flex bg-[#11590a] w-full items-center justify-between lg:px-0 px-3">
              <h1 class="lg:block lg:mb-10 flex italic font-semibold lg:text-white text-white"><span
                  class="uppercase block 2xl:text-6xl xl:text-5xl lg:text-4xl lg:mt-9">tingog&nbsp;</span><span
                  class="capitalize 2xl:text-4xl xl:text-3xl lg:text-2xl"> student publications</span></h1>
              <button
                class="w-fit cursor-pointer lg:mb-10 my-2 text-left text-xs uppercase lg:text-green-900 text-white lg:bg-white 2xl:px-7 xl:px-4 px-3 2xl:py-3.5 xl:py-2.5 lg:py-2 block"><span
                class="lg:block hidden font-bold">learn more</span><span class="lg:hidden block"><i
                class="fa fa-angle-double-right text-xl"></i></span></button>
            </div>
          </a>
        </div>
      </div>
    </div>
    <!-- <div class="lg:block hidden">
      <div class="h-1 w-full bg-white">
      </div>
      <div class="h-2 w-full bg-green-700">
      </div>
      <div class="h-1 w-full bg-white">
      </div>
    </div>
    <div class="h-2 w-full bg-green-700">
    </div>
    <div class="h-1 w-full bg-white">
    </div> -->
  </div>
</template>